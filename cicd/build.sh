#!/bin/bash
# The value below is the default predefined variable from CodeBuild
# https://github.com/aws/aws-codebuild-docker-images/blob/master/ubuntu/standard/7.0/Dockerfile
export JAVA_HOME="$JAVA_21_HOME"
export JRE_HOME="$JRE_21_HOME"
export JDK_HOME="$JDK_21_HOME"
QUARKUS_CONTAINER_IMAGE_TAG=$(./gradlew -q getImageTag )
export TOKEN=$(echo $GITHUB_TOKEN | jq -r '.SecretString')
export GIT_USERNAME="hx-devops"
echo $QUARKUS_CONTAINER_IMAGE_TAG
./gradlew -version
./gradlew clean build -Dquarkus.container-image.build=true -Dquarkus.container-image.image=$QUARKUS_CONTAINER_IMAGE_TAG
docker tag $QUARKUS_CONTAINER_IMAGE_TAG  $REPOSITORY_URI:$TAG
docker rmi $QUARKUS_CONTAINER_IMAGE_TAG
