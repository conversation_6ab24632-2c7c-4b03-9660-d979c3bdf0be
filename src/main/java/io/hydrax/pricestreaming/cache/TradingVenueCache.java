package io.hydrax.pricestreaming.cache;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.TimeInForce;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TradingVenueCache {
  Map<String, TradingVenueDTO> tradingVenues = new ConcurrentHashMap<>();
  Map<String, List<String>> symbolCodes = new ConcurrentHashMap<>();

  public TradingVenueDTO get(String marketCode, String venueMarketCode) {
    return tradingVenues.get(joinKey(marketCode, venueMarketCode));
  }

  public void put(VenueMarketUpdateRequest request) {
    // Generate a TradingVenue instance based on the request
    TradingVenueDTO venue = TradingVenueDTO.from(request);
    this.put(request.getMarketCode(), request.getCode(), venue);
  }

  public void put(String marketCode, String venueMarketCode, TradingVenueDTO venue) {
    // Update the tradingVenues map with the new or updated TradingVenue data
    tradingVenues.put(joinKey(marketCode, venueMarketCode), venue);

    // Update the symbolCodes cache
    // Remove any existing cache entries related to the current venue
    symbolCodes.entrySet().removeIf(entry -> entry.getKey().startsWith(venueMarketCode + "_"));

    // Add new symbolCodes data to the cache
    venue
        .getTickersRoute()
        .forEach(
            tickerRoute -> {
              String lpTickerName = tickerRoute.getLpTickerName();
              String cacheKey = venueMarketCode + "_" + lpTickerName;

              // Update the cache with the new ticker codes for the given lpTickerName
              symbolCodes.computeIfAbsent(
                  cacheKey,
                  key ->
                      venue.getTickersRoute().stream()
                          .filter(
                              ticker ->
                                  lpTickerName.equals(
                                      ticker.getLpTickerName())) // Filter by lpTickerName
                          .map(TickerRoute::getTickerCode) // Extract tickerCode
                          .toList() // Collect as an immutable list
                  );
            });
  }

  public List<TradingVenueDTO> getAll() {
    return List.copyOf(tradingVenues.values());
  }

  public void remove(VenueMarketUpdateRequest request) {
    tradingVenues.remove(joinKey(request.getMarketCode(), request.getCode()));
  }

  public List<String> selectCodeByTimeInForceAndOrderType(
      String timeInForce, String orderType, String ticker) {
    log.trace("tradingVenues: {}", tradingVenues);
    log.trace("timeInForce: {}, orderType: {}, ticker: {}", timeInForce, orderType, ticker);
    return tradingVenues.values().stream()
        .filter(
            venue -> {
              // check if the venue contains the order type and time in force
              boolean hasOrderType = venue.getOrderTypes().contains(OrderType.from(orderType));
              // Check if the map contains the orderType key
              Set<TimeInForce> timeInForceSet = venue.getTimeInForces().get(orderType);
              boolean hasTimeInForce =
                  timeInForceSet != null
                      && timeInForceSet.contains(TimeInForceEnum.from(timeInForce));

              // If the venue does not contain the order type or time in force, return false
              if (!hasOrderType || !hasTimeInForce) {
                return false;
              }

              // Extract the ticker codes from the venue (handle potential null case)
              Set<String> tickerCodes =
                  Optional.ofNullable(venue.getTickersRoute())
                      .orElse(Collections.emptyList()) // Avoid NullPointerException
                      .stream()
                      .map(TickerRoute::getTickerCode)
                      .collect(Collectors.toSet());

              // Check if the venue contains the ticker code
              return tickerCodes.contains(ticker);
            })
        .map(TradingVenueDTO::getCode)
        .toList();
  }

  String joinKey(String marketCode, String venueMarketCode) {
    return marketCode + ":" + venueMarketCode;
  }

  public String getLpTickerName(String marketCode, String venueMarketCode, String symbol) {
    return this.get(marketCode, venueMarketCode).getTickersRoute().stream()
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getMarketCodeByVenueCode(String venueCode) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getCode().equals(venueCode)) // Filter by code
        .map(TradingVenueDTO::getMarketCode) // Get market code
        .toList();
  }

  public String getLpTickerName(String marketCode, String symbol) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getMarketCode().equals(marketCode))
        .flatMap(v -> v.getTickersRoute().stream())
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getSymbolCodesByVenueMarketAndVenueSymbol(
      String venueMarketCode, String lpTickerName) {
    // Generate the cache key by combining marketCode and lpTickerName.
    String cacheKey = venueMarketCode + "_" + lpTickerName;

    // Retrieve the ticker codes from the cache if available; otherwise, compute and cache the
    // result.
    return symbolCodes.computeIfAbsent(
        cacheKey,
        key ->
            tradingVenues.values().stream()
                .filter(venue -> venueMarketCode.equals(venue.getCode())) // Filter by marketCode.
                .flatMap(
                    venue -> venue.getTickersRoute().stream()) // Flatten the list of ticker routes.
                .filter(
                    ticker ->
                        lpTickerName.equals(ticker.getLpTickerName())) // Filter by lpTickerName.
                .map(TickerRoute::getTickerCode) // Extract the tickerCode.
                .toList() // Collect as an immutable list.
        );
  }
}
