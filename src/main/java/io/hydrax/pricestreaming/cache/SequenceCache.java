package io.hydrax.pricestreaming.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import lombok.Getter;

@Getter
public class SequenceCache {
  final Map<String, AtomicLong> sequenceMap = new ConcurrentHashMap<>();

  public long get(String key) {
    return sequenceMap.computeIfAbsent(key, k -> new AtomicLong(0)).get();
  }

  public AtomicLong getAtomic(String key) {
    return sequenceMap.computeIfAbsent(key, k -> new AtomicLong(0));
  }

  public void put(String key, long value) {
    this.getAtomic(key).set(value);
  }

  public Map<String, Long> getAll() {
    return Map.copyOf(sequenceMap).entrySet().stream()
        .collect(
            ConcurrentHashMap::new, (m, e) -> m.put(e.getKey(), e.getValue().get()), Map::putAll);
  }
}
