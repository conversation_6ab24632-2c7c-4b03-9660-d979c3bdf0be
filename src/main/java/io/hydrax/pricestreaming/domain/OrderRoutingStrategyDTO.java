package io.hydrax.pricestreaming.domain;

import io.hydrax.pricestreaming.common.RoutingStrategyType;
import io.hydrax.pricestreaming.domain.entity.OrderRoutingStrategy;
import io.hydrax.pricestreaming.router.Rule;
import io.hydrax.proto.metwo.match.OrderRoutingStrategyUpdateRequest;
import io.hydrax.proto.metwo.match.PsOrderType;
import io.hydrax.proto.metwo.match.SequenceVenueMarket;
import io.hydrax.proto.metwo.match.TimeInForce;
import java.util.*;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OrderRoutingStrategyDTO implements Rule {
  private Integer id;

  private String code;

  private String createTime;

  private Integer createUid;

  private String name;

  private Integer operatorId;

  private String type;

  private String writeTime;

  private Integer writeUid;

  private Set<PsOrderType> orderTypes;

  private Map<String, Set<TimeInForce>> timeInForces;

  private List<String> tradingVenues;

  private List<String> tickers;

  private String orsOrderType;

  @Override
  public List<String> getTickers() {
    return tickers;
  }

  @Override
  public Set<PsOrderType> getOrdTypes() {
    if (orderTypes == null) {
      return Collections.emptySet();
    }
    return orderTypes;
  }

  @Override
  public List<String> getVenueMarkets() {
    return tradingVenues;
  }

  @Override
  public Set<TimeInForce> getTimeInForces(String orderType) {
    Set<TimeInForce> set = timeInForces.get(orderType);
    if (set == null) {
      return Collections.emptySet();
    }
    return set;
  }

  public static OrderRoutingStrategyDTO from(OrderRoutingStrategyUpdateRequest request) {
    OrsOrderType orderType = OrsOrderType.parse(request.getOrsOrderType());
    List<String> venueCodes;
    if (RoutingStrategyType.SEQUENCE.getCode().equals(request.getType())) {
      venueCodes =
          request.getSequenceVenueMarketsList().stream()
              .sorted(Comparator.comparing(SequenceVenueMarket::getSequence))
              .map(SequenceVenueMarket::getVenueMarketCode)
              .toList();
    } else {
      venueCodes = request.getVenueMarketCodesList();
    }
    return OrderRoutingStrategyDTO.builder()
        .id((int) request.getDbId())
        .name(request.getName())
        .code(request.getCode())
        .type(request.getType())
        .orderTypes(orderType.getOrderTypes())
        .timeInForces(orderType.getTimeInForces())
        .tradingVenues(venueCodes)
        .tickers(request.getTickerCodesList())
        .orsOrderType(request.getOrsOrderType())
        .build();
  }

  public static OrderRoutingStrategyUpdateRequest toProto(
      OrderRoutingStrategyDTO orderRoutingStrategyDTO, List<String> sequenceVenues) {
    OrderRoutingStrategyUpdateRequest.Builder builder =
        OrderRoutingStrategyUpdateRequest.newBuilder();
    if (orderRoutingStrategyDTO.getId() != null) {
      builder.setDbId(orderRoutingStrategyDTO.getId());
    }
    if (orderRoutingStrategyDTO.getName() != null) {
      builder.setName(orderRoutingStrategyDTO.getName());
    }
    if (orderRoutingStrategyDTO.getCode() != null) {
      builder.setCode(orderRoutingStrategyDTO.getCode());
    }
    if (orderRoutingStrategyDTO.getType() != null) {
      builder.setType(orderRoutingStrategyDTO.getType());
    }
    if (orderRoutingStrategyDTO.getTradingVenues() != null) {
      builder.addAllVenueMarketCodes(orderRoutingStrategyDTO.getTradingVenues());
    }
    if (orderRoutingStrategyDTO.getTickers() != null) {
      builder.addAllTickerCodes(orderRoutingStrategyDTO.getTickers());
    }
    if (orderRoutingStrategyDTO.getOrsOrderType() != null) {
      builder.setOrsOrderType(orderRoutingStrategyDTO.getOrsOrderType());
    }
    if (sequenceVenues != null && !sequenceVenues.isEmpty()) {
      for (int i = 0; i < sequenceVenues.size(); i++) {
        builder.addSequenceVenueMarkets(
            SequenceVenueMarket.newBuilder()
                .setSequence(i)
                .setVenueMarketCode(sequenceVenues.get(i))
                .build());
      }
    }
    return builder.build();
  }

  public static OrderRoutingStrategyDTO from(OrderRoutingStrategy orderRoutingStrategy) {
    OrsOrderType orderType = OrsOrderType.parse(orderRoutingStrategy.getOrsOrderTypes());
    return OrderRoutingStrategyDTO.builder()
        .id(orderRoutingStrategy.getId())
        .code(orderRoutingStrategy.getCode())
        .createTime(orderRoutingStrategy.getCreateTime().toString())
        .createUid(orderRoutingStrategy.getCreateUid())
        .name(orderRoutingStrategy.getName())
        .operatorId(orderRoutingStrategy.getOperatorId())
        .type(orderRoutingStrategy.getType())
        .writeTime(orderRoutingStrategy.getWriteTime().toString())
        .writeUid(orderRoutingStrategy.getWriteUid())
        .orsOrderType(orderRoutingStrategy.getOrsOrderTypes())
        .orderTypes(orderType.getOrderTypes())
        .timeInForces(orderType.getTimeInForces())
        .build();
  }
}
