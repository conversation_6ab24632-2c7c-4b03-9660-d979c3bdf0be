package io.hydrax.pricestreaming.domain;

import io.hydrax.aeron.AeronMessage;
import io.hydrax.pricestreaming.exception.GenericException;
import io.hydrax.proto.metwo.match.*;
import java.lang.reflect.InvocationTargetException;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
public class ERResponseList implements AeronMessage {

  private ResponseList responseList;
  private String topic;

  @Override
  public byte[] toByteArray() {
    return responseList.toByteArray();
  }

  @Override
  public String topic() {
    return topic;
  }

  @Override
  @SuppressWarnings("unchecked")
  public <T> T parse(byte[] bytes, Class<T> clazz) {
    try {
      return (T) clazz.getMethod("parseFrom", byte[].class).invoke(null, bytes);
    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
      throw new GenericException("can not parse bytes.", e);
    }
  }
}
