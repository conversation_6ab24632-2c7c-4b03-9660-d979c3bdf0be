package io.hydrax.pricestreaming.domain;

import io.hydrax.aeron.AeronMessage;
import io.hydrax.pricestreaming.exception.GenericException;
import io.hydrax.proto.metwo.match.SORReject;
import java.lang.reflect.InvocationTargetException;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RejectionDTO implements AeronMessage {
  SORReject sorReject;

  @Override
  public byte[] toByteArray() {
    return sorReject.toByteArray();
  }

  @Override
  public String topic() {
    return "reject-internal";
  }

  @Override
  public <T> T parse(byte[] bytes, Class<T> clazz) {
    try {
      return (T) clazz.getMethod("parseFrom", byte[].class).invoke(null, bytes);
    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
      throw new GenericException("can not parse bytes.", e);
    }
  }
}
