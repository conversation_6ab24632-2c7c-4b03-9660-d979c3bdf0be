package io.hydrax.pricestreaming.domain;

import io.hydrax.aeron.AeronMessage;
import io.hydrax.pricestreaming.exception.GenericException;
import io.hydrax.proto.metwo.match.Request;
import java.lang.reflect.InvocationTargetException;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class PlaceOrder implements AeronMessage {
  Request request;
  String topic;

  @Override
  public byte[] toByteArray() {
    return request.toByteArray();
  }

  @Override
  public String topic() {
    return topic;
  }

  @Override
  @SuppressWarnings("unchecked")
  public <T> T parse(byte[] bytes, Class<T> clazz) {
    try {
      return (T) clazz.getMethod("parseFrom", byte[].class).invoke(null, (Object) bytes);
    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
      throw new GenericException("can not parse bytes.", e);
    }
  }
}
