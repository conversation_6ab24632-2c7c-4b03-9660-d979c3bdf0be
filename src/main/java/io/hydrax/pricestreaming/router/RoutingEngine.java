package io.hydrax.pricestreaming.router;

import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.exception.RejectionException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class RoutingEngine {
  private final Supplier<List<Rule>> rules;

  public void route(Order order, List<String> venueCodes) {
    if (log.isTraceEnabled()) {
      rules
          .get()
          .forEach(
              rule ->
                  log.trace(
                      "rule: {}, \\n timeinforce: {}, \\n orderType: {}, \\n ticker: {}",
                      rule,
                      order.getPsOrder().getTimeInForce(),
                      order.getPsOrder().getOrdType(),
                      order.getPsOrder().getSymbol()));
    }
    HashSet<String> venueSets = new HashSet<>(venueCodes);
    Rule matchedRule =
        rules.get().stream()
            .filter(
                rule ->
                    rule.match(order) && !Collections.disjoint(venueSets, rule.getVenueMarkets()))
            .findFirst()
            .orElseThrow(() -> new RejectionException("no matched rule"));

    log.trace("matched rule: {}", matchedRule);
    venueSets.retainAll(matchedRule.getVenueMarkets());
    List<String> venues = new ArrayList<>(venueSets);
    matchedRule.handle(order, venues);
  }
}
