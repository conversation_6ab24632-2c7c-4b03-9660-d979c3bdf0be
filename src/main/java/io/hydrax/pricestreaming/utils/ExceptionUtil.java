package io.hydrax.pricestreaming.utils;

import static io.hydrax.pricestreaming.common.Constant.OUT_SEQUENCE;

import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.proto.metwo.match.*;

public class ExceptionUtil {
  private ExceptionUtil() {}

  public static ERResponseList buildRejectionER(PsOrder order, String reason) {
    return ERResponseList.builder()
        .topic(Topic.ER)
        .responseList(
            ResponseList.newBuilder()
                .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                .addResponses(
                    Response.newBuilder()
                        .setPsParentOrderExecReport(buildParentOrderExecReport(order, reason))
                        .build())
                .build())
        .build();
  }

  public static PsParentOrderExecReport buildParentOrderExecReport(PsOrder order, String reason) {
    return PsParentOrderExecReport.newBuilder()
        .setOrderId(order.getOrderId())
        .setError(reason)
        .setEarmarkAmt(order.getEarmarkAmt())
        .setPsExecType(PsExecType.PS_EXEC_TYPE_REJECTED)
        .setPsOrderType(order.getOrdType())
        .setExternalOrderId(order.getClOrdId())
        .setBaseBalanceAccountId(order.getBaseBalanceAccountId())
        .setQuoteBalanceAccountId(order.getQuoteBalanceAccountId())
        .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_REJECTED)
        .setQuantity(order.getQty()) // Quantity
        .setPrice(order.getPrice())
        .setSide(order.getSide()) // Side (Buy/Sell)
        .setSymbol(order.getSymbol()) // Symbol
        .setProcessedTime(System.currentTimeMillis())
        .setServiceAccountId(order.getServiceAccountId()) // Service Account ID
        .setAssetHoldingAccountId(order.getHoldingAccountId())
        .setTimeInForce(order.getTimeInForce()) // Time in Force
        .setTraceId(order.getTraceId()) // Transaction Time
        .setUserId(order.getUserId()) // User ID
        .setExpiryTime(order.getExpireTime())
        .build();
  }
}
