package io.hydrax.pricestreaming.utils;

import io.hydrax.proto.metwo.match.UDec128;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import org.apache.commons.lang3.StringUtils;

public final class UDec128Util {
  private static final int SIXTY_FOUR_BIT = 64;
  private static final int SCALE = 18;
  private static final BigDecimal BD_TEN_POWER_OF_SCALE = BigDecimal.TEN.pow(SCALE);
  public static final UDec128 ZERO = UDec128.newBuilder().build();

  private UDec128Util() {
    throw new AssertionError("No instances for you!");
  }

  public static BigDecimal toBigDecimal(final UDec128 uDec128) {
    var high = new BigInteger(Long.toUnsignedString(uDec128.getHigh()));
    var low = new BigInteger(Long.toUnsignedString(uDec128.getLow()));
    var bigInteger = high.shiftLeft(SIXTY_FOUR_BIT).or(low);
    return new BigDecimal(bigInteger).divide(BD_TEN_POWER_OF_SCALE, MathContext.DECIMAL128);
  }

  public static UDec128 from(final BigDecimal bdValue) {
    return fromBigDecimalToUDec128(null, bdValue);
  }

  public static UDec128 from(UDec128.Builder builder, BigDecimal val) {
    return fromBigDecimalToUDec128(builder, val);
  }

  private static UDec128 fromBigDecimalToUDec128(UDec128.Builder builder, BigDecimal val) {
    if (null == builder) {
      builder = UDec128.newBuilder();
    } else {
      builder.clear();
    }
    var decimal128 = val.multiply(BD_TEN_POWER_OF_SCALE).toBigInteger();
    var low = decimal128.longValue();
    var high = decimal128.shiftRight(SIXTY_FOUR_BIT).longValue();
    return builder.setHigh(high).setLow(low).build();
  }

  public static UDec128 from(final String strVal) {
    if (StringUtils.isBlank(strVal)) {
      return UDec128.newBuilder().build();
    }
    return fromBigDecimalToUDec128(null, new BigDecimal(strVal));
  }

  public static boolean isPositive(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }
    try {
      return (new BigDecimal(str)).compareTo(BigDecimal.ZERO) > 0;
    } catch (NumberFormatException e) {
      // silent
      return false;
    }
  }

  /** comparative: a < b */
  public static boolean isLessThan(UDec128 a, UDec128 b) {
    if (a.getHigh() != b.getHigh()) {
      return Long.compareUnsigned(a.getHigh(), b.getHigh()) < 0;
    }
    return Long.compareUnsigned(a.getLow(), b.getLow()) < 0;
  }

  /** comparative: a > b */
  public static boolean isGreaterThan(UDec128 a, UDec128 b) {
    if (a.getHigh() != b.getHigh()) {
      return Long.compareUnsigned(a.getHigh(), b.getHigh()) > 0;
    }
    return Long.compareUnsigned(a.getLow(), b.getLow()) > 0;
  }

  /** comparative: a == b */
  public static boolean isEqual(UDec128 a, UDec128 b) {
    return a.getHigh() == b.getHigh() && a.getLow() == b.getLow();
  }

  public static boolean isGreaterThanOrEqualTo(UDec128 a, UDec128 b) {
    return isGreaterThan(a, b) || isEqual(a, b);
  }

  public static boolean isLessThanOrEqualTo(UDec128 a, UDec128 b) {
    return isLessThan(a, b) || isEqual(a, b);
  }

  /** add: a + b */
  public static UDec128 add(UDec128 a, UDec128 b) {
    long low = a.getLow() + b.getLow();
    long carry = Long.compareUnsigned(low, a.getLow()) < 0 ? 1 : 0;
    long high = a.getHigh() + b.getHigh() + carry;
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** subtract: a - b (unsigned subtraction, make sure a >= b) */
  public static UDec128 subtract(UDec128 a, UDec128 b) {
    if (isLessThan(a, b)) {
      throw new ArithmeticException("Result would be negative in unsigned arithmetic.");
    }
    long borrow = Long.compareUnsigned(a.getLow(), b.getLow()) < 0 ? 1 : 0;
    long low = a.getLow() - b.getLow();
    long high = a.getHigh() - b.getHigh() - borrow;
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** multiply: a * b / 10^18 */
  public static UDec128 multiply(UDec128 a, UDec128 b) {
    BigInteger bigA = toBigInteger(a);
    BigInteger bigB = toBigInteger(b);
    BigInteger result = bigA.multiply(bigB).divide(BD_TEN_POWER_OF_SCALE.toBigIntegerExact());
    return fromBigInteger(result);
  }

  /** divide: (a * 10^18) / b */
  public static UDec128 divide(UDec128 dividend, UDec128 divisor) {
    if (divisor.getHigh() == 0 && divisor.getLow() == 0) {
      throw new ArithmeticException("Division by zero");
    }
    BigInteger bigDividend =
        toBigInteger(dividend).multiply(BD_TEN_POWER_OF_SCALE.toBigIntegerExact());
    BigInteger bigDivisor = toBigInteger(divisor);
    BigInteger result = bigDividend.divide(bigDivisor);
    return fromBigInteger(result);
  }

  /** UDec128 convert BigInteger */
  private static BigInteger toBigInteger(UDec128 uDec) {
    BigInteger high = new BigInteger(Long.toUnsignedString(uDec.getHigh()));
    BigInteger low = new BigInteger(Long.toUnsignedString(uDec.getLow()));
    return high.shiftLeft(SIXTY_FOUR_BIT).or(low);
  }

  /** BigInteger convert UDec128 */
  private static UDec128 fromBigInteger(BigInteger bigInt) {
    BigInteger mask = BigInteger.valueOf(2).pow(64).subtract(BigInteger.ONE);
    long low = bigInt.and(mask).longValue();
    long high = bigInt.shiftRight(64).longValue();
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** check if UDec128 is zero */
  public static boolean isZero(UDec128 value) {
    return value.getHigh() == 0 && value.getLow() == 0;
  }

  public static UDec128 min(UDec128 a, UDec128 b) {
    return UDec128Util.toBigDecimal(a).compareTo(UDec128Util.toBigDecimal(b)) <= 0 ? a : b;
  }

  public static UDec128 roundDown(UDec128 value, int scale) {
    BigDecimal bdValue = UDec128Util.toBigDecimal(value);
    BigDecimal rounded = bdValue.setScale(scale, RoundingMode.DOWN);
    return UDec128Util.from(rounded);
  }
}
