package io.hydrax.pricestreaming.config;

import io.hydrax.pricestreaming.cache.*;
import io.quarkus.runtime.Startup;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Singleton
@RequiredArgsConstructor
@Slf4j
@Startup
public class CacheConfig {

  @Produces
  @Singleton
  @Startup
  public OrderBookCache orderBook() {
    return new OrderBookCache();
  }

  @Produces
  @Singleton
  @Startup
  public TradingVenueCache tradingVenue() {
    return new TradingVenueCache();
  }

  @Produces
  @Singleton
  @Startup
  public OrderRoutingStrategyCache orderRoutingStrategy(StrategyConfig strategyConfig) {
    return new OrderRoutingStrategyCache();
  }

  @Produces
  @Singleton
  @Startup
  public TradingVenueAccountCache tradingVenueAccount() {
    return new TradingVenueAccountCache();
  }

  @Produces
  @Singleton
  @Startup
  public SequenceVenueCache sequenceVenue() {
    return new SequenceVenueCache();
  }

  @Produces
  @Singleton
  @Startup
  public OrderCache orderCache() {
    return new OrderCache();
  }

  @Produces
  @Singleton
  @Startup
  public TickerCache tickerCache() {
    return new TickerCache();
  }

  @Produces
  @Singleton
  @Startup
  public CumulativeOrderBookCache cumulativeOrderBookCache() {
    return new CumulativeOrderBookCache();
  }

  @Produces
  @Singleton
  @Startup
  public SequenceCache sequenceCache() {
    return new SequenceCache();
  }
}
