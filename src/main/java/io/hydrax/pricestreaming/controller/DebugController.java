package io.hydrax.pricestreaming.controller;

import static io.hydrax.pricestreaming.common.Constant.*;
import static io.hydrax.proto.metwo.match.PsOrderType.PS_ORDER_TYPE_LIMIT;
import static io.hydrax.proto.metwo.match.PsOrderType.PS_ORDER_TYPE_MARKET;
import static io.hydrax.proto.metwo.match.Side.SIDE_BUY;
import static io.hydrax.proto.metwo.match.Side.SIDE_SELL;
import static io.hydrax.proto.metwo.match.TimeInForce.TIME_IN_FORCE_DAY;
import static io.hydrax.proto.metwo.match.TimeInForce.TIME_IN_FORCE_FILL_OR_KILL;
import static io.hydrax.proto.metwo.match.TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL;
import static io.hydrax.proto.metwo.match.TimeInForce.TIME_IN_FORCE_GOOD_TILL_DATE;
import static io.hydrax.proto.metwo.match.TimeInForce.TIME_IN_FORCE_IMMEDIATE_OR_CANCEL;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.aeron.performence.JMHTask;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.OrderBookMessage;
import io.hydrax.pricestreaming.domain.entity.OrderRoutingStrategy;
import io.hydrax.pricestreaming.repository.OrderRoutingStrategyRepository;
import io.hydrax.pricestreaming.utils.OrderStatusConverter;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.resteasy.reactive.RestQuery;
import org.openjdk.jmh.results.format.ResultFormatType;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.openjdk.jmh.runner.options.TimeValue;

@Slf4j
@Path("/debug")
@ApplicationScoped
@RequiredArgsConstructor
public class DebugController {

  // Shared variable for parentOrderId
  private String parentOrderId = "";
  private String childOrderId = "";
  final ClientManager clientManager;
  final OrderRoutingStrategyRepository orderRoutingStrategyRepository;

  // Define scale factor (10^18 for precision
  static final BigDecimal scaleFactor = new BigDecimal("1000000000000000000");

  @ConfigProperty(name = "enable-rand-seed", defaultValue = "false")
  boolean enableRandSeed;

  @ConfigProperty(name = "rand-seed", defaultValue = "0")
  int randSeed;

  private final Random random = new Random(randSeed);

  public static String generateOrderId() {
    return UUID.randomUUID().toString().replace("-", "").substring(0, 22);
  }

  // Generate a random traceId (32-character hex string) by using UUID and removing dashes
  public static String generateTraceId() {
    return UUID.randomUUID().toString().replace("-", "");
  }

  @POST
  @Path("/ps/parent-order-er")
  public String sendPsParentOrderEr(Map<String, Object> requestBody) {
    log.debug(
        "[*PRICE_STREAMING*] Send price streaming parent order from requestBody: {}", requestBody);
    String serviceAccessAccountUuid = (String) requestBody.get("serviceAccessAccountUuid");
    String assetHoldingAccountUuid = (String) requestBody.get("assetHoldingAccountUuid");
    String extOrdId = (String) requestBody.get("extOrdId");
    String side = (String) requestBody.get("side");
    Side sideProto = convertToProtoSideFromString(side);
    String symbolCode = (String) requestBody.get("symbol");
    String orderType = (String) requestBody.get("orderType");
    PsOrderType orderTypeProto = convertToProtoOrderTypeFromString(orderType);
    String timeInForce = (String) requestBody.get("timeInForce");
    TimeInForce timeInForceProto = convertToProtoTimeInForceFromString(timeInForce);

    long quantityLowValue = convertToProtoLowValue(requestBody, "orderQuantity");
    long priceLowValue = convertToProtoLowValue(requestBody, "orderPrice");
    long estimatedFeesLowValue = convertToProtoLowValue(requestBody, "estimatedFees");

    String userLogin = (String) requestBody.get("userLogin");
    // Get the current timestamp in milliseconds
    long currentTimestamp = System.currentTimeMillis();

    // Calculate the timestamp for 7 days later (7 days * 24 hours * 60 minutes * 60 seconds * 1000
    // milliseconds)
    long sevenDaysLater = currentTimestamp + TimeUnit.DAYS.toMillis(7);

    // Generate a random parent orderId
    parentOrderId = generateOrderId();

    // Generate a random traceId
    String traceId = generateTraceId();

    PsParentOrderExecReport.Builder psParentOrderExecReportBuilder =
        PsParentOrderExecReport.newBuilder()
            .setServiceAccountId(serviceAccessAccountUuid)
            .setAssetHoldingAccountId(assetHoldingAccountUuid)
            .setExternalOrderId(extOrdId)
            .setSide(sideProto)
            .setSymbol(symbolCode)
            .setPsOrderType(orderTypeProto)
            .setTimeInForce(timeInForceProto)
            .setOrderStatus(PsParentOrderStatus.PS_PARENT_ORDER_STATUS_PENDING)
            .setEstimatedFees(UDec128.newBuilder().setHigh(0).setLow(estimatedFeesLowValue).build())
            .setExpiryTime(sevenDaysLater)
            .setOrderId(parentOrderId)
            .setQuantity(UDec128.newBuilder().setHigh(0).setLow(quantityLowValue).build())
            .setPrice(UDec128.newBuilder().setHigh(0).setLow(priceLowValue).build())
            .setFromService("sor")
            .setProcessedTime(currentTimestamp)
            .setTraceId(traceId)
            .setUserId(userLogin);

    ERResponseList executionReport =
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(psParentOrderExecReportBuilder.build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build();
    log.debug("[*PRICE_STREAMING*] Send parent order exec report: {}", executionReport);
    clientManager.send(executionReport);
    return "Send price streaming parent order exec report success";
  }

  public static long convertToProtoLowValue(Map<String, Object> requestBody, String key) {
    String objectStr = (String) requestBody.get(key);
    BigDecimal objectBigDecimal = new BigDecimal(objectStr);
    BigDecimal objectLowValue = objectBigDecimal.multiply(scaleFactor);
    return objectLowValue.longValue();
  }

  public static Side convertToProtoSideFromString(String side) {
    switch (side.toUpperCase()) {
      case "B":
        return SIDE_BUY;
      case "S":
        return SIDE_SELL;
      default:
        throw new IllegalArgumentException("Unknown side: " + side);
    }
  }

  public static PsOrderType convertToProtoOrderTypeFromString(String orderType) {
    switch (orderType.toUpperCase()) {
      case "MKT":
        return PS_ORDER_TYPE_MARKET;
      case "LMT":
        return PS_ORDER_TYPE_LIMIT;
      default:
        throw new IllegalArgumentException("Unknown orderType: " + orderType);
    }
  }

  public static TimeInForce convertToProtoTimeInForceFromString(String timeInForce) {
    switch (timeInForce.toUpperCase()) {
      case "DAY":
        return TIME_IN_FORCE_DAY;
      case "GTC":
        return TIME_IN_FORCE_GOOD_TILL_CANCEL;
      case "GTD":
        return TIME_IN_FORCE_GOOD_TILL_DATE;
      case "IOC":
        return TIME_IN_FORCE_IMMEDIATE_OR_CANCEL;
      case "FOK":
        return TIME_IN_FORCE_FILL_OR_KILL;
      default:
        throw new IllegalArgumentException("Unknown timeInForce: " + timeInForce);
    }
  }

  @POST
  @Path("/ps/child-order-er")
  public String sendPsChildOrderEr(Map<String, Object> requestBody) {
    log.debug(
        "[*PRICE_STREAMING*] Send price streaming child order from requestBody: {}", requestBody);
    String assetHoldingAccountUuid = (String) requestBody.get("assetHoldingAccountUuid");
    String side = (String) requestBody.get("side");
    Side sideProto = convertToProtoSideFromString(side);
    String symbolCode = (String) requestBody.get("symbol");
    String orderType = (String) requestBody.get("orderType");
    PsOrderType orderTypeProto = convertToProtoOrderTypeFromString(orderType);
    String timeInForce = (String) requestBody.get("timeInForce");
    TimeInForce timeInForceProto = convertToProtoTimeInForceFromString(timeInForce);

    long quantityLowValue = convertToProtoLowValue(requestBody, "orderQuantity");
    long priceLowValue = convertToProtoLowValue(requestBody, "orderPrice");

    String userLogin = (String) requestBody.get("userLogin");
    String marketModel = (String) requestBody.get("marketModel");

    // Get the current timestamp in milliseconds
    long currentTimestamp = System.currentTimeMillis();

    // Generate a random child orderId
    childOrderId = generateOrderId();

    PsChildOrderExecReport.Builder psChildOrderExecReportBuilder =
        PsChildOrderExecReport.newBuilder()
            .setAssetHoldingAccountId(assetHoldingAccountUuid)
            .setSide(sideProto)
            .setSymbol(symbolCode)
            .setPsOrderType(orderTypeProto)
            .setProcessedTime(currentTimestamp)
            .setTimeInForce(timeInForceProto)
            .setQuantity(UDec128.newBuilder().setHigh(0).setLow(quantityLowValue).build())
            .setPrice(UDec128.newBuilder().setHigh(0).setLow(priceLowValue).build())
            .setUserId(userLogin)
            .setMarketModel(marketModel)
            .setPsExecType(PsExecType.PS_EXEC_TYPE_NEW)
            .setFromService("sor")
            .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_SOR)
            .setFixRequestId(childOrderId)
            .setExecId(childOrderId)
            .setOrderId(childOrderId)
            .setOrderStatus(PsChildOrderStatus.PS_CHILD_ORDER_STATUS_PENDING)
            .setParentOrderId(parentOrderId)
            .setTransactTime(currentTimestamp)
            .setVenueAccount("PS_Member_FZ_001_AHA_001")
            .setVenueCode("SFOX")
            .setVenueFees("5.00")
            .setVenueSymbol("BTC/USD");

    ERResponseList executionReport =
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsChildOrderExecReport(psChildOrderExecReportBuilder.build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build();

    clientManager.send(executionReport);
    log.debug("[*PRICE_STREAMING*] Send child order exec report: {}", executionReport);
    return "Send price streaming child order exec report success";
  }

  @POST
  @Path("/ps/trade-er")
  public String sendPsTradeEr(Map<String, Object> requestBody) {
    log.debug("[*PRICE_STREAMING*] Send price streaming trade from requestBody: {}", requestBody);
    String assetHoldingAccountUuid = (String) requestBody.get("assetHoldingAccountUuid");
    String side = (String) requestBody.get("side");
    Side sideProto = convertToProtoSideFromString(side);
    String symbolCode = (String) requestBody.get("symbol");
    String timeInForce = (String) requestBody.get("timeInForce");
    TimeInForce timeInForceProto = convertToProtoTimeInForceFromString(timeInForce);
    String extOrdId = (String) requestBody.get("extOrdId");

    long quantityLowValue = convertToProtoLowValue(requestBody, "orderQuantity");
    long priceLowValue = convertToProtoLowValue(requestBody, "orderPrice");
    long estimatedFeesLowValue = convertToProtoLowValue(requestBody, "estimatedFees");
    long includePremiumPriceLowValue = convertToProtoLowValue(requestBody, "includePremiumPrice");
    long earmarkAmountLowValue = convertToProtoLowValue(requestBody, "earmarkAmount");

    String userLogin = (String) requestBody.get("userLogin");
    String marketModel = (String) requestBody.get("marketModel");
    String premium = (String) requestBody.get("premium");

    String baseBalanceAccountId = (String) requestBody.get("baseBalanceAccountId");
    String quoteBalanceAccountId = (String) requestBody.get("quoteBalanceAccountId");

    // Get the current timestamp in nanoseconds
    long currentTimestamp = System.currentTimeMillis() * 1_000_000;

    String psTradeId;
    if (enableRandSeed) {
      // TODO: backup random seed
      // Generate random long numbers between 0 and 100_000_000_000_000_000
      long randomNumber = Math.abs(random.nextLong() % 100_000_000_000_000L);
      // Formatted to 14 bits using String.format, with 0's in front of the missing bits.
      psTradeId = String.format("T-%014d", randomNumber);
    } else {
      UUID uuid = UUID.randomUUID();
      String numericPart = uuid.toString().replaceAll("[^0-9]", "");
      String trimmedNumericPart = numericPart.substring(0, 14);
      psTradeId = "T-" + trimmedNumericPart;
    }
    log.debug("[*PRICE_STREAMING*] Send trade exec report, psTradeId: {}", psTradeId);
    UDec128 includePremiumPrice =
        UDec128.newBuilder().setHigh(0).setLow(includePremiumPriceLowValue).build();

    UDec128 lastFillQuantity = UDec128.newBuilder().setHigh(0).setLow(quantityLowValue).build();

    PsChildOrderExecReport.Builder psChildOrderExecReportBuilder =
        PsChildOrderExecReport.newBuilder()
            .setAssetHoldingAccountId(assetHoldingAccountUuid)
            .setSide(sideProto)
            .setSymbol(symbolCode)
            .setTimeInForce(timeInForceProto)
            .setLastFillQuantity(lastFillQuantity)
            .setLastFillPrice(UDec128.newBuilder().setHigh(0).setLow(priceLowValue).build())
            .setUserId(userLogin)
            .setMarketModel(marketModel)
            .setEstimatedFees(UDec128.newBuilder().setHigh(0).setLow(estimatedFeesLowValue).build())
            .setIncludePremiumPrice(includePremiumPrice)
            .setPremium(UDec128Util.from(premium))
            .setPsExecType(PsExecType.PS_EXEC_TYPE_TRADE)
            .setFromService("sor")
            .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_SOR)
            .setPsTradeId(psTradeId)
            .setOrderStatus(PsChildOrderStatus.PS_CHILD_ORDER_STATUS_CONFIRMED)
            .setPsTradeStatus(PsTradeStatus.PS_TRADE_STATUS_CONFIRMED)
            .setFixRequestId(childOrderId)
            .setParentOrderId(parentOrderId)
            .setOrderId(childOrderId)
            .setProcessedTime(currentTimestamp)
            .setVenueAccount("PS_Member_FZ_001_AHA_001")
            .setVenueFees("5.00");

    ERResponseList executionReport =
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsChildOrderExecReport(psChildOrderExecReportBuilder.build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build();
    log.debug("[*PRICE_STREAMING*] Trade send exec report: {}", executionReport);
    clientManager.send(executionReport);

    // Send Mock Price Update Event
    PriceUpdate.Builder priceUpdateBuilder = PriceUpdate.newBuilder();
    priceUpdateBuilder.setSymbol(symbolCode);
    String timezone = "Asia/Singapore";
    priceUpdateBuilder.setTimezone(timezone);
    priceUpdateBuilder.setLastTradePrice(includePremiumPrice);
    priceUpdateBuilder.setLastTradeQty(lastFillQuantity);
    priceUpdateBuilder.setLastTradeTime(currentTimestamp);
    priceUpdateBuilder.setFromService("sor");

    log.debug("[*PRICE_STREAMING*] Price Update send priceUpdateBuilder: {}", priceUpdateBuilder);
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setFromService("sor")
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(Response.newBuilder().setPriceUpdate(priceUpdateBuilder).build())
                    .build())
            .topic(Topic.ER)
            .build());

    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(
                                PsParentOrderExecReport.newBuilder()
                                    .setServiceAccountId(
                                        psChildOrderExecReportBuilder.getServiceAccountId())
                                    .setAssetHoldingAccountId(
                                        psChildOrderExecReportBuilder.getAssetHoldingAccountId())
                                    .setSide(psChildOrderExecReportBuilder.getSide())
                                    .setSymbol(psChildOrderExecReportBuilder.getSymbol())
                                    .setPsOrderType(psChildOrderExecReportBuilder.getPsOrderType())
                                    .setTimeInForce(psChildOrderExecReportBuilder.getTimeInForce())
                                    .setQuantity(psChildOrderExecReportBuilder.getQuantity())
                                    .setPrice(psChildOrderExecReportBuilder.getPrice())
                                    .setUserId(psChildOrderExecReportBuilder.getUserId())
                                    .setEarmarkAmt(
                                        UDec128.newBuilder()
                                            .setHigh(0)
                                            .setLow(earmarkAmountLowValue)
                                            .build())
                                    .setBaseBalanceAccountId(baseBalanceAccountId)
                                    .setQuoteBalanceAccountId(quoteBalanceAccountId)
                                    .setOrderId(parentOrderId)
                                    .setFromService("sor")
                                    .setExternalOrderId(extOrdId)
                                    .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_LP)
                                    .setPsExecType(psChildOrderExecReportBuilder.getPsExecType())
                                    .setLastFillPrice(
                                        psChildOrderExecReportBuilder.getLastFillPrice())
                                    .setLastFillQuantity(
                                        psChildOrderExecReportBuilder.getLastFillQuantity())
                                    .setLastTradeId(psTradeId)
                                    .setCumulativeFillQuantity(
                                        psChildOrderExecReportBuilder.getLastFillQuantity())
                                    .setCumulativeFillAmount(
                                        psChildOrderExecReportBuilder.getCumulativeFillAmount())
                                    .setProcessedTime(System.currentTimeMillis())
                                    .setOrderStatus(
                                        OrderStatusConverter.convertToOrderStatus(
                                            psChildOrderExecReportBuilder.getOrderStatus()))
                                    .build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build());
    return "Send price streaming trade exec report success";
  }

  @POST
  @Path("/ps/reject-er")
  public String sendPsRejectEr(Map<String, Object> requestBody) {
    log.debug(
        "[*PRICE_STREAMING*] Send price streaming reject order from requestBody: {}", requestBody);
    String assetHoldingAccountUuid = (String) requestBody.get("assetHoldingAccountUuid");
    String side = (String) requestBody.get("side");
    Side sideProto = convertToProtoSideFromString(side);
    String symbolCode = (String) requestBody.get("symbol");
    String timeInForce = (String) requestBody.get("timeInForce");
    TimeInForce timeInForceProto = convertToProtoTimeInForceFromString(timeInForce);
    long estimatedFeesLowValue = convertToProtoLowValue(requestBody, "estimatedFees");
    String userLogin = (String) requestBody.get("userLogin");
    String marketModel = (String) requestBody.get("marketModel");
    long earmarkAmountLowValue = convertToProtoLowValue(requestBody, "earmarkAmount");
    String baseBalanceAccountId = (String) requestBody.get("baseBalanceAccountId");
    String quoteBalanceAccountId = (String) requestBody.get("quoteBalanceAccountId");
    String marketCode = (String) requestBody.get("marketCode");

    // Get the current timestamp in milliseconds
    long currentTimestamp = System.currentTimeMillis();

    PsChildOrderExecReport.Builder psChildOrderExecReportBuilder =
        PsChildOrderExecReport.newBuilder()
            .setAssetHoldingAccountId(assetHoldingAccountUuid)
            .setSide(sideProto)
            .setSymbol(symbolCode)
            .setTimeInForce(timeInForceProto)
            .setUserId(userLogin)
            .setEstimatedFees(UDec128.newBuilder().setHigh(0).setLow(estimatedFeesLowValue).build())
            .setMarketModel(marketModel)
            .setPsExecType(PsExecType.PS_EXEC_TYPE_REJECTED)
            .setOrderStatus(PsChildOrderStatus.PS_CHILD_ORDER_STATUS_REJECTED)
            .setFromService("sor")
            .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_SOR)
            .setFixRequestId(childOrderId)
            .setParentOrderId(parentOrderId)
            .setOrderId(childOrderId)
            .setProcessedTime(currentTimestamp)
            .setVenueAccount("PS_Member_FZ_001_AHA_001")
            .setVenueFees("5.00");

    ERResponseList executionReport =
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsChildOrderExecReport(psChildOrderExecReportBuilder.build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build();
    log.debug("[*PRICE_STREAMING*] Send reject exec report: {}", executionReport);
    clientManager.send(executionReport);
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(
                                PsParentOrderExecReport.newBuilder()
                                    .setServiceAccountId(
                                        psChildOrderExecReportBuilder.getServiceAccountId())
                                    .setAssetHoldingAccountId(
                                        psChildOrderExecReportBuilder.getAssetHoldingAccountId())
                                    .setSide(psChildOrderExecReportBuilder.getSide())
                                    .setSymbol(psChildOrderExecReportBuilder.getSymbol())
                                    .setPsOrderType(psChildOrderExecReportBuilder.getPsOrderType())
                                    .setTimeInForce(psChildOrderExecReportBuilder.getTimeInForce())
                                    .setUserId(psChildOrderExecReportBuilder.getUserId())
                                    .setEarmarkAmt(
                                        UDec128.newBuilder()
                                            .setHigh(0)
                                            .setLow(earmarkAmountLowValue)
                                            .build())
                                    .setBaseBalanceAccountId(baseBalanceAccountId)
                                    .setQuoteBalanceAccountId(quoteBalanceAccountId)
                                    .setMarketCode(marketCode)
                                    .setOrderId(parentOrderId)
                                    .setFromService("sor")
                                    .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_LP)
                                    .setPsExecType(psChildOrderExecReportBuilder.getPsExecType())
                                    .setProcessedTime(System.currentTimeMillis())
                                    .setCumulativeFillQuantity(
                                        psChildOrderExecReportBuilder.getLastFillQuantity())
                                    .setCumulativeFillAmount(
                                        psChildOrderExecReportBuilder.getCumulativeFillAmount())
                                    .setOrderStatus(
                                        OrderStatusConverter.convertToOrderStatus(
                                            psChildOrderExecReportBuilder.getOrderStatus()))
                                    .build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build());
    return "Send price streaming reject exec report success";
  }

  @POST
  @Path("/ps/cancel-er")
  public String sendPsCancelEr(Map<String, Object> requestBody) {
    log.debug(
        "[*PRICE_STREAMING*] Send price streaming cancel order from requestBody: {}", requestBody);
    String assetHoldingAccountUuid = (String) requestBody.get("assetHoldingAccountUuid");
    String side = (String) requestBody.get("side");
    Side sideProto = convertToProtoSideFromString(side);
    String symbolCode = (String) requestBody.get("symbol");
    String timeInForce = (String) requestBody.get("timeInForce");
    TimeInForce timeInForceProto = convertToProtoTimeInForceFromString(timeInForce);
    String userLogin = (String) requestBody.get("userLogin");
    String marketModel = (String) requestBody.get("marketModel");

    long estimatedFeesLowValue = convertToProtoLowValue(requestBody, "estimatedFees");
    long earmarkAmountLowValue = convertToProtoLowValue(requestBody, "earmarkAmount");

    String baseBalanceAccountId = (String) requestBody.get("baseBalanceAccountId");
    String quoteBalanceAccountId = (String) requestBody.get("quoteBalanceAccountId");
    String marketCode = (String) requestBody.get("marketCode");

    // Get the current timestamp in milliseconds
    long currentTimestamp = System.currentTimeMillis();

    PsChildOrderExecReport.Builder psChildOrderExecReportBuilder =
        PsChildOrderExecReport.newBuilder()
            .setAssetHoldingAccountId(assetHoldingAccountUuid)
            .setSide(sideProto)
            .setSymbol(symbolCode)
            .setTimeInForce(timeInForceProto)
            .setUserId(userLogin)
            .setMarketModel(marketModel)
            .setEstimatedFees(UDec128.newBuilder().setHigh(0).setLow(estimatedFeesLowValue).build())
            .setPsExecType(PsExecType.PS_EXEC_TYPE_CANCELED)
            .setFromService("sor")
            .setOrderStatus(PsChildOrderStatus.PS_CHILD_ORDER_STATUS_CANCELED)
            .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_SOR)
            .setFixRequestId(childOrderId)
            .setParentOrderId(parentOrderId)
            .setOrderId(childOrderId)
            .setProcessedTime(currentTimestamp)
            .setVenueAccount("PS_Member_FZ_001_AHA_001")
            .setVenueFees("5.00");

    ERResponseList executionReport =
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsChildOrderExecReport(psChildOrderExecReportBuilder.build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build();
    log.debug("[*PRICE_STREAMING*] Send cancel exec report: {}", executionReport);
    clientManager.send(executionReport);
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(
                                PsParentOrderExecReport.newBuilder()
                                    .setServiceAccountId(
                                        psChildOrderExecReportBuilder.getServiceAccountId())
                                    .setAssetHoldingAccountId(
                                        psChildOrderExecReportBuilder.getAssetHoldingAccountId())
                                    .setSide(psChildOrderExecReportBuilder.getSide())
                                    .setSymbol(psChildOrderExecReportBuilder.getSymbol())
                                    .setPsOrderType(psChildOrderExecReportBuilder.getPsOrderType())
                                    .setTimeInForce(psChildOrderExecReportBuilder.getTimeInForce())
                                    .setUserId(psChildOrderExecReportBuilder.getUserId())
                                    .setEarmarkAmt(
                                        UDec128.newBuilder()
                                            .setHigh(0)
                                            .setLow(earmarkAmountLowValue)
                                            .build())
                                    .setBaseBalanceAccountId(baseBalanceAccountId)
                                    .setQuoteBalanceAccountId(quoteBalanceAccountId)
                                    .setMarketCode(marketCode)
                                    .setOrderId(parentOrderId)
                                    .setFromService("sor")
                                    .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_LP)
                                    .setPsExecType(psChildOrderExecReportBuilder.getPsExecType())
                                    .setProcessedTime(System.currentTimeMillis())
                                    .setCumulativeFillQuantity(
                                        psChildOrderExecReportBuilder.getLastFillQuantity())
                                    .setCumulativeFillAmount(
                                        psChildOrderExecReportBuilder.getCumulativeFillAmount())
                                    .setOrderStatus(
                                        OrderStatusConverter.convertToOrderStatus(
                                            psChildOrderExecReportBuilder.getOrderStatus()))
                                    .build())
                            .build())
                    .build())
            .topic(Topic.ER)
            .build());
    return "Send price streaming cancel exec report success";
  }

  @GET
  @Path("/jmh")
  public String jmh(@RestQuery Integer count, @RestQuery Integer time, @RestQuery Integer warmUp)
      throws RunnerException {
    Options opt =
        new OptionsBuilder()
            .timeout(TimeValue.seconds(30))
            .warmupIterations(warmUp)
            .measurementTime(new TimeValue(time, TimeUnit.SECONDS))
            .include(JMHTask.class.getSimpleName())
            .measurementIterations(count)
            .forks(0)
            .resultFormat(ResultFormatType.JSON)
            .result("/Volumes/RAMDisk/deployments/result.json")
            .build();
    new Runner(opt).run();
    return "jmh";
  }

  @POST
  @Path("/ps/order-book")
  public String sendPsOrderBook(Map<String, Object> requestBody) {
    log.debug(
        "[*PRICE_STREAMING*] Send price streaming order book from requestBody: {}", requestBody);

    // Parse the request body
    List<Map<String, Object>> bids = (List<Map<String, Object>>) requestBody.get("bids");
    List<Map<String, Object>> asks = (List<Map<String, Object>>) requestBody.get("asks");
    String symbolCode = (String) requestBody.get("symbolCode");

    // Create an instance of GetOrderbookResp.Builder
    GetOrderbookResp.Builder orderBookResponse = GetOrderbookResp.newBuilder();

    // Set the symbol field
    orderBookResponse.setSymbol(symbolCode);

    // Define scale factor (10^18 for precision

    // Create and add bid price levels
    if (bids != null) {
      for (int i = 0; i < bids.size(); i++) {
        Map<String, Object> bid = bids.get(i);

        long lowBidPriceValue = convertToProtoLowValue(bid, "price");
        long lowBidQuantityValue = convertToProtoLowValue(bid, "quantity");

        PriceLevel priceLevel =
            PriceLevel.newBuilder()
                .setPrice(UDec128.newBuilder().setHigh(0).setLow(lowBidPriceValue))
                .setQty(UDec128.newBuilder().setHigh(0).setLow(lowBidQuantityValue))
                .setOffset(i + 1)
                .setDepth(i + 1)
                .setTime(System.currentTimeMillis())
                .setNumOrders(10) // Example static value
                .build();
        orderBookResponse.addBids(priceLevel);
      }
    }

    // Create and add ask price levels
    if (asks != null) {
      for (int i = 0; i < asks.size(); i++) {
        Map<String, Object> ask = asks.get(i);
        long askPrice = Long.parseLong((String) ask.get("price"));
        long askQuantity = Long.parseLong((String) ask.get("quantity"));

        // Calculate the low value based on price
        long lowAskPriceValue = convertToProtoLowValue(ask, "price");
        long lowAskQuantityValue = convertToProtoLowValue(ask, "quantity");

        PriceLevel priceLevel =
            PriceLevel.newBuilder()
                .setPrice(UDec128.newBuilder().setHigh(0).setLow(lowAskPriceValue))
                .setQty(UDec128.newBuilder().setHigh(0).setLow(lowAskQuantityValue))
                .setOffset(i + 1)
                .setDepth(i + 1)
                .setTime(System.currentTimeMillis())
                .setNumOrders(10) // Example static value
                .build();
        orderBookResponse.addAsks(priceLevel);
      }
    }

    // Set the last trade price
    long lastTradePriceLowValue = convertToProtoLowValue(requestBody, "lastTradePrice");
    orderBookResponse.setLastTradePrice(
        UDec128.newBuilder().setHigh(0).setLow(lastTradePriceLowValue));

    // Set other static fields
    long lastTradeQuantityLowValue = convertToProtoLowValue(requestBody, "lastTradeQuantity");
    orderBookResponse.setLastTradeQty(
        UDec128.newBuilder().setHigh(0).setLow(lastTradeQuantityLowValue));

    orderBookResponse.setLastTradeTime(System.currentTimeMillis());

    // Use GetOrderBookResp Build Response
    Response response = Response.newBuilder().setGetOrderbook(orderBookResponse).build();

    ResponseList responseList = ResponseList.newBuilder().addResponses(response).build();

    // Build OrderBookMessage
    OrderBookMessage orderBookMessage =
        OrderBookMessage.builder()
            .request(Request.newBuilder().setMatchResponses(responseList).build())
            .topic(Topic.ORDER_BOOK)
            .build();

    log.debug("[*PRICE_STREAMING*] Send price streaming order book: {}", orderBookMessage);
    clientManager.send(orderBookMessage);

    return "Send price streaming order book success";
  }

  @GET
  @Path("/sequence")
  public String sync(
      @RestQuery Optional<Integer> id,
      @RestQuery Optional<Integer> out,
      @RestQuery Optional<Integer> pus) {
    log.debug("sync sequence, id sequence: {}, out sequence: {}", id, out);
    id.ifPresent(ID_SEQUENCE::set);
    out.ifPresent(OUT_SEQUENCE::set);
    pus.ifPresent(PRICE_UPDATE_SEQUENCE::set);
    return "sync success";
  }

  @GET
  @Path("/order-strategy")
  public Uni<List<OrderRoutingStrategy>> getOrderRoutingStrategy() {
    return Panache.withSession(orderRoutingStrategyRepository::listAll);
  }
}
