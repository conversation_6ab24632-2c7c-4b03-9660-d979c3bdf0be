package io.hydrax.pricestreaming.controller;

import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.domain.TickerDTO;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.pricestreaming.router.Rule;
import jakarta.inject.Singleton;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/cache")
@Singleton
@RequiredArgsConstructor
public class CacheController {

  final TradingVenueCache tradingVenueCache;
  final TradingVenueAccountCache tradingVenueAccountCache;
  final TickerCache tickerCache;
  final OrderRoutingStrategyCache orderRoutingStrategyCache;
  final OrderCache orderCache;

  @Path("/venue-markets")
  @GET
  public List<TradingVenueDTO> getVenueMarkets() {
    log.info("getVenueMarkets");
    return tradingVenueCache.getAll();
  }

  @Path("/venue-accounts")
  @GET
  public List<TradingVenueAccountDTO> getVenueAccounts() {
    log.info("getVenueAccounts");
    return tradingVenueAccountCache.getAll();
  }

  @Path("/tickers")
  @GET
  public List<TickerDTO> getTickers() {
    log.info("getTickers");
    return tickerCache.getAll();
  }

  @Path("/strategy")
  @GET
  public List<Rule> getStrategies() {
    log.info("getStrategies");
    return orderRoutingStrategyCache.getAll();
  }

  @Path("/open-orders/count")
  @GET
  public long countParentOrder() {
    log.info("getOpenOrders");
    return orderCache.countParentOrder();
  }
}
