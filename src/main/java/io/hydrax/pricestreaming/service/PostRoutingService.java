package io.hydrax.pricestreaming.service;

import static io.hydrax.pricestreaming.common.Constant.OUT_SEQUENCE;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.config.MarketModelServiceFactory;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.PlaceOrder;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.exception.RejectionException;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.pricestreaming.utils.TopicUtil;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@Singleton
@RequiredArgsConstructor
@Slf4j
public class PostRoutingService {
  final OrderService orderService;
  final OrderCache orderCache;
  final MarketCache marketCache;
  final MarketModelServiceFactory marketModelServiceFactory;
  final ClientManager clientManager;
  final TradingVenueAccountService tradingVenueAccountService;

  @ConfigProperty(name = "POD_NAME")
  String podName;

  public void postProcess(String venueCode, PsOrder psOrder, String lpTickerName) {
    String marketModel = marketCache.getMarketModelBySymbolCode(psOrder.getSymbol());
    if (marketModel == null) {
      throw new RejectionException("No market model found!");
    }
    MarketModelService marketModelService = marketModelServiceFactory.get(marketModel);
    TradingVenueAccountDTO venueAccount =
        tradingVenueAccountService.getTradingVenueAccount(venueCode, psOrder, marketModel);
    PsParentOrderExecReport psParentOrderExecReport = orderService.generateParentOrder(psOrder);
    String id = psOrder.getOrderId() + "-01";
    Long parentOrderId = IdUtil.formatId(psOrder.getOrderId());
    orderCache.put(id, parentOrderId);
    orderCache.putChildOrderList(parentOrderId, List.of(id));
    orderCache.putChildOrder(id, venueCode);

    var price = psOrder.getPrice();
    BigDecimal new_price = UDec128Util.toBigDecimal(price);
    if (psOrder.getSide() == Side.SIDE_SELL) {
      new_price = new_price.add(UDec128Util.toBigDecimal(psOrder.getPremium()));
    } else {
      new_price = new_price.subtract(UDec128Util.toBigDecimal(psOrder.getPremium()));
    }
    UDec128 premium = psOrder.getPremium();
    price = UDec128Util.from(new_price);
    PsOrder orderWithoutPremium =
        psOrder.toBuilder().setPrice(price).setPremium(premium).setMarketModel(marketModel).build();

    ResponseList responseList =
        marketModelService
            .buildChildOrder(orderWithoutPremium, venueCode, id, lpTickerName)
            .addResponses(
                Response.newBuilder().setPsParentOrderExecReport(psParentOrderExecReport).build())
            .setOutSequence(OUT_SEQUENCE.getAndIncrement())
            .setFromService("sor")
            .build();
    clientManager.send(ERResponseList.builder().topic(Topic.ER).responseList(responseList).build());
    PsOrder placeOrder =
        orderWithoutPremium.toBuilder()
            .setVenueAccountName(venueAccount.getVenueAccount())
            .setOrderId(id)
            .setFixRequestId(id)
            .setClOrdId(id)
            .setSymbol(lpTickerName)
            .build();
    clientManager.send(
        PlaceOrder.builder()
            .topic(TopicUtil.create(Topic.ORDER, venueCode))
            .request(Request.newBuilder().setFromService(podName).setPsOrder(placeOrder).build())
            .build());
  }
}
