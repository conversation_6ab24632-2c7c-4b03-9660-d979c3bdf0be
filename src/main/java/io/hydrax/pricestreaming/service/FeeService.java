package io.hydrax.pricestreaming.service;

import static io.hydrax.pricestreaming.domain.FeeStructureConfig.FeeTypeConfig;

import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.domain.FeeLevelConfig;
import io.hydrax.pricestreaming.domain.FeeStructureConfig;
import io.hydrax.pricestreaming.domain.Tax;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.FeeLevel;
import io.hydrax.proto.metwo.match.FeeResponse;
import io.hydrax.proto.metwo.match.FeeStructure;
import io.hydrax.proto.metwo.match.UDec128;
import it.unimi.dsi.fastutil.Pair;
import jakarta.enterprise.context.ApplicationScoped;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationScoped
public class FeeService {

  final OrderCache orderCache;

  public static Map<String, UDec128> getPsTradeFeeAndTax(
      String orderId,
      FeeResponse resp,
      UDec128 tradeQuantity,
      UDec128 tradeAmount,
      UDec128 remainingEarmarkFee,
      UDec128 remainingEarmarkTax) {
    log.debug(
        "Calculating fee and tax for order: {}, FeeResponse: {}, tradeQuantity: {}, tradeAmount:"
            + " {}, remainingEarmarkFee: {}, remainingEarmarkTax: {}",
        orderId,
        resp,
        tradeQuantity,
        tradeAmount,
        remainingEarmarkFee,
        remainingEarmarkTax);
    UDec128 feeAmount = UDec128Util.ZERO;
    UDec128 taxAmount = UDec128Util.ZERO;
    Pair<List<FeeStructureConfig>, List<Tax>> feeAndTaxpair = parseFeeTaxStructure(resp);
    List<FeeStructureConfig> feeStructureConfigs = feeAndTaxpair.first();
    List<Tax> taxs = feeAndTaxpair.second();
    Pair<UDec128, Boolean> psTradeTaxPercentage = getPsTradeTaxPercentage(taxs);
    UDec128 tradeTaxPct = psTradeTaxPercentage.first();
    boolean hasTradeTaxConf = psTradeTaxPercentage.second();
    for (FeeStructureConfig feeStructureConfig : feeStructureConfigs) {
      UDec128 amountToUse = tradeQuantity;
      if (feeStructureConfig.deriveOnAmount) {
        amountToUse = tradeAmount;
      }
      UDec128 derivedTradeFee = calcFees(amountToUse, feeStructureConfig);
      log.debug(
          "Derived trade fee for order: {}, derivedTradeFee: {}, feeStructureConfig: {}",
          orderId,
          derivedTradeFee,
          feeStructureConfig);
      if (!UDec128Util.isZero(derivedTradeFee)) {
        feeAmount = UDec128Util.add(feeAmount, derivedTradeFee);
      }
    }

    boolean hasFeeAmt = !UDec128Util.isZero(feeAmount);
    if (hasFeeAmt && hasTradeTaxConf) {
      taxAmount =
          UDec128Util.divide(
              UDec128Util.multiply(feeAmount, tradeTaxPct),
              UDec128Util.from(new BigDecimal("100")));
    }

    return calculateReleaseEarmarkFeeAndTax(
        orderId, feeAmount, remainingEarmarkFee, taxAmount, remainingEarmarkTax);
  }

  public static Pair<List<FeeStructureConfig>, List<Tax>> parseFeeTaxStructure(FeeResponse resp) {
    if (resp == null || resp.getTradeFeeRulesConfList().isEmpty()) {
      return Pair.of(new ArrayList<>(), new ArrayList<>());
    }

    List<FeeStructureConfig> feeStructureConfigs = new ArrayList<>();
    for (FeeStructure feeStructure : resp.getTradeFeeRulesConfList()) {
      FeeStructureConfig feeStructureConfig = new FeeStructureConfig();
      switch (feeStructure.getFeeType()) {
        case FEE_TIERED:
          feeStructureConfig.feeTypeConfig = FeeTypeConfig.FEE_TIERED;
          break;
        case FEE_BANDED:
          feeStructureConfig.feeTypeConfig = FeeTypeConfig.FEE_BANDED;
          break;
        case FEE_TIERED_RATIO:
          feeStructureConfig.feeTypeConfig = FeeTypeConfig.FEE_TIERED_RATIO;
          break;
        case FEE_BANDED_RATIO:
          feeStructureConfig.feeTypeConfig = FeeTypeConfig.FEE_BANDED_RATIO;
          break;
      }

      feeStructureConfig.description = feeStructure.getDescription();
      feeStructureConfig.deriveOnAmount = feeStructure.getFeeBase();
      feeStructureConfig.minFee = feeStructure.getMinFee();
      feeStructureConfig.maxFee = feeStructure.getMaxFee();
      feeStructureConfig.ratioX = feeStructure.getRatioX();
      feeStructureConfig.ratioY = feeStructure.getRatioY();

      for (FeeLevel feeLevel : feeStructure.getFeeLevelsList()) {
        FeeLevelConfig feeLevelConfig = new FeeLevelConfig();
        feeLevelConfig.minAmount = feeLevel.getMinAmount();
        feeLevelConfig.maxAmount = feeLevel.getMaxAmount();
        feeLevelConfig.fixedFee = feeLevel.getFixedFee();
        feeLevelConfig.basisPoints = feeLevel.getBasisPoints();
        feeLevelConfig.levelId = feeLevel.getLevelId();
        feeStructureConfig.feeLevelConfigs.add(feeLevelConfig);
      }

      feeStructureConfigs.add(feeStructureConfig);
    }

    List<Tax> taxs = new ArrayList<>();
    if (!resp.getTradeTaxConfList().isEmpty()) {
      for (io.hydrax.proto.metwo.match.Tax tradeTaxConf : resp.getTradeTaxConfList()) {
        Tax tax = new Tax();
        tax.taxConfigId = tradeTaxConf.getTaxConfigId();
        tax.startDatetimeUTC = tradeTaxConf.getStartDatetimeUtc();
        tax.endDatetimeUTC = tradeTaxConf.getEndDatetimeUtc();
        tax.percentage = tradeTaxConf.getTaxPercentage();
        tax.cacheKey = tradeTaxConf.getCacheKey();
        tax.userLogin = tradeTaxConf.getUserLogin();
        taxs.add(tax);
      }
    }

    return Pair.of(feeStructureConfigs, taxs);
  }

  public static Pair<UDec128, Boolean> getPsTradeTaxPercentage(List<Tax> taxs) {
    if (taxs == null || taxs.isEmpty()) {
      return Pair.of(UDec128Util.ZERO, false);
    }

    String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    for (Tax tax : taxs) {
      if (now.compareTo(tax.startDatetimeUTC) >= 0 && now.compareTo(tax.endDatetimeUTC) <= 0) {
        return Pair.of(tax.percentage, true);
      }
    }
    return Pair.of(UDec128Util.ZERO, false);
  }

  public static UDec128 calcFees(UDec128 amount, FeeStructureConfig feeStruct) {
    return switch (feeStruct.feeTypeConfig) {
      case FEE_TIERED -> calcTieredFee(amount, feeStruct);
      case FEE_BANDED -> calcBandedFee(amount, feeStruct);
      case FEE_TIERED_RATIO -> calcRatioFee(amount, feeStruct);
      default -> UDec128Util.ZERO;
    };
  }

  public static Pair<UDec128, UDec128> getLowerAndUpperBound(FeeLevelConfig level, UDec128 txnAmt) {
    UDec128 upper = level.maxAmount;
    UDec128 lower = UDec128Util.ZERO;

    if (UDec128Util.isZero(level.maxAmount)) {
      upper = txnAmt;
    }

    if (!UDec128Util.isZero(level.minAmount)) {
      lower = level.minAmount;
    }

    return Pair.of(lower, upper);
  }

  public static UDec128 calcTieredFee(UDec128 amount, FeeStructureConfig feeStructureConfig) {
    for (FeeLevelConfig feeLevelConfig : feeStructureConfig.feeLevelConfigs) {
      Pair<UDec128, UDec128> lowerAndUpperBound = getLowerAndUpperBound(feeLevelConfig, amount);
      UDec128 lower = lowerAndUpperBound.first();
      UDec128 upper = lowerAndUpperBound.second();

      if (UDec128Util.isGreaterThan(amount, lower)
          && UDec128Util.isLessThanOrEqualTo(amount, upper)) {
        UDec128 fee = UDec128Util.ZERO;
        if (feeLevelConfig.fixedFee != null) fee = UDec128Util.add(fee, feeLevelConfig.fixedFee);
        if (feeLevelConfig.basisPoints != null) {
          fee =
              UDec128Util.add(
                  fee,
                  UDec128Util.divide(
                      UDec128Util.multiply(amount, feeLevelConfig.basisPoints),
                      UDec128Util.from(new BigDecimal("10000"))));
        }
        return fee;
      }
    }
    return UDec128Util.ZERO;
  }

  public static UDec128 calcBandedFee(UDec128 amount, FeeStructureConfig feeStructureConfig) {
    UDec128 totalFee = UDec128Util.ZERO;

    for (FeeLevelConfig feeLevelConfig : feeStructureConfig.feeLevelConfigs) {
      Pair<UDec128, UDec128> lowerAndUpperBound = getLowerAndUpperBound(feeLevelConfig, amount);
      UDec128 lower = lowerAndUpperBound.first();
      UDec128 upper = lowerAndUpperBound.second();
      if (UDec128Util.isGreaterThan(amount, lower)) {
        UDec128 minValue = UDec128Util.min(amount, upper);
        UDec128 subValue = UDec128Util.subtract(minValue, lower);
        UDec128 multiplyValue = UDec128Util.multiply(subValue, feeLevelConfig.basisPoints);
        UDec128 divideValue =
            UDec128Util.divide(multiplyValue, UDec128Util.from(new BigDecimal("10000")));
        UDec128 fee = UDec128Util.add(divideValue, feeLevelConfig.fixedFee);
        totalFee = UDec128Util.add(totalFee, fee);
      }
    }
    return totalFee;
  }

  public static UDec128 calcRatioFee(UDec128 amount, FeeStructureConfig feeStructureConfig) {
    UDec128 fee = UDec128Util.divide(amount, feeStructureConfig.ratioX);
    fee = UDec128Util.roundDown(fee, 0);
    return UDec128Util.multiply(fee, feeStructureConfig.ratioY);
  }

  public static Map<String, UDec128> calculateReleaseEarmarkFeeAndTax(
      String orderId,
      UDec128 feeAmount,
      UDec128 remainingEarmarkFee,
      UDec128 taxAmount,
      UDec128 remainingEarmarkTax) {
    UDec128 releaseEarmarkFee;
    UDec128 releaseEarmarkTax;
    log.debug(
        "calculateReleaseEarmarkFeeAndTax orderId: {}, feeAmount: {}, remainingEarmarkFee: {},"
            + " taxAmount: {}, remainingEarmarkTax: {}",
        orderId,
        feeAmount,
        remainingEarmarkFee,
        taxAmount,
        remainingEarmarkTax);

    if (UDec128Util.isZero(feeAmount)
        || UDec128Util.isGreaterThanOrEqualTo(feeAmount, remainingEarmarkFee)) {
      releaseEarmarkFee = remainingEarmarkFee;
      remainingEarmarkFee = UDec128Util.ZERO;
    } else {
      releaseEarmarkFee = feeAmount;
      remainingEarmarkFee = UDec128Util.subtract(remainingEarmarkFee, feeAmount);
    }

    if (UDec128Util.isZero(taxAmount)
        || UDec128Util.isGreaterThanOrEqualTo(taxAmount, remainingEarmarkTax)) {
      releaseEarmarkTax = remainingEarmarkTax;
      remainingEarmarkTax = UDec128Util.ZERO;
    } else {
      releaseEarmarkTax = taxAmount;
      remainingEarmarkTax = UDec128Util.subtract(remainingEarmarkTax, taxAmount);
    }
    log.debug(
        "calculateReleaseEarmarkFeeAndTax done, orderId: {}, releaseEarmarkFee: {},"
            + " releaseEarmarkTax: {}, remainingEarmarkFee: {}, remainingEarmarkTax: {}",
        orderId,
        releaseEarmarkFee,
        releaseEarmarkTax,
        remainingEarmarkFee,
        remainingEarmarkTax);
    Map<String, UDec128> params = new HashMap<>();
    params.put("releaseEarmarkFee", releaseEarmarkFee);
    params.put("releaseEarmarkTax", releaseEarmarkTax);
    params.put("remainingEarmarkFee", remainingEarmarkFee);
    params.put("remainingEarmarkTax", remainingEarmarkTax);

    return params;
  }
}
