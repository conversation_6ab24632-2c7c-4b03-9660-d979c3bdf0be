package io.hydrax.pricestreaming.service;

import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@Singleton
@RequiredArgsConstructor
@Startup
@Slf4j
public class KubernetesPodService {
  final KubernetesClient client;

  @ConfigProperty(name = "POD_NAME", defaultValue = "smart-order-routing")
  String podName;

  @ConfigProperty(name = "NAMESPACE", defaultValue = "default")
  String namespace;

  public void updatePodAnnotation(boolean isLeader) {
    Pod pod = client.pods().inNamespace(namespace).withName(podName).get();
    pod.getMetadata()
        .getAnnotations()
        .put("smart-order-routing/cluster-role", isLeader ? "leader" : "follower");
    client.pods().inNamespace(namespace).withName(podName).patch(pod);
    log.info(
        "Updated pod annotation: {} in namespace: {} with role: {}",
        podName,
        namespace,
        isLeader ? "leader" : "follower");
  }

  @PostConstruct
  public void init() {
    log.info(
        "Initializing Kubernetes Pod Service with pod name: {} and namespace: {}",
        podName,
        namespace);
    updatePodAnnotation(false);
  }
}
