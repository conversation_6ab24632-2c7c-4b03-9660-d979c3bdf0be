package io.hydrax.pricestreaming.aeron.service;

import io.aeron.archive.client.AeronArchive;
import io.aeron.cluster.service.ClusteredServiceContainer;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.IdelStrategyEnum;
import io.hydrax.aeron.config.AeronClientProperty;
import io.hydrax.aeron.utils.ArchiveUtil;
import io.hydrax.pricestreaming.cache.SequenceCache;
import io.quarkus.runtime.Quarkus;
import io.quarkus.runtime.Startup;
import io.vertx.core.eventbus.EventBus;
import jakarta.annotation.Priority;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Startup
@Slf4j
public class ServiceAgent {

  final AeronClientProperty aeronClientProperty;
  final EventBus eventBus;
  final ClientManager clientManager;
  final SequenceCache sequenceCache;

  @Produces
  @ApplicationScoped
  @Startup
  @Priority(5)
  public ClusteredServiceContainer clusteredServiceContainer() {
    final AeronArchive.Context aeronArchiveContext = ArchiveUtil.createContext(aeronClientProperty);
    return ClusteredServiceContainer.launch(
        new ClusteredServiceContainer.Context()
            .idleStrategySupplier(
                () ->
                    IdelStrategyEnum.getIdleStrategy(aeronClientProperty.service().idleStrategy()))
            .errorHandler(
                e -> {
                  log.error("Error In Smart Order Routing Service", e);
                  Quarkus.asyncExit();
                })
            .aeronDirectoryName(aeronClientProperty.directory())
            .clusterDirectoryName(aeronClientProperty.cluster().directory())
            .archiveContext(aeronArchiveContext)
            .clusteredService(new SORService(eventBus, clientManager, sequenceCache)));
  }
}
