package io.hydrax.pricestreaming.aeron.service;

import com.google.protobuf.InvalidProtocolBufferException;
import io.aeron.ExclusivePublication;
import io.aeron.FragmentAssembler;
import io.aeron.Image;
import io.aeron.cluster.codecs.CloseReason;
import io.aeron.cluster.service.ClientSession;
import io.aeron.cluster.service.Cluster;
import io.aeron.cluster.service.ClusteredService;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.IdelStrategyEnum;
import io.hydrax.pricestreaming.cache.SequenceCache;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.exception.GenericException;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.EventBus;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.agrona.DirectBuffer;
import org.agrona.collections.MutableBoolean;
import org.agrona.concurrent.AtomicBuffer;
import org.agrona.concurrent.IdleStrategy;
import org.agrona.concurrent.UnsafeBuffer;

@Slf4j
@RequiredArgsConstructor
public class SORService implements ClusteredService {

  final EventBus eventBus;
  final ClientManager clientManager;
  final IdleStrategy idleStrategy = IdelStrategyEnum.YIELD.getIdleStrategy();
  final SequenceCache sequenceCache;

  @Override
  public void onStart(Cluster cluster, Image image) {
    clientManager.setCluster(cluster);
    log.info("SORService started");
    if (null != image) {
      loadSnapshot(image);
    }
    loadFromDataBase();
  }

  private void loadFromDataBase() {
    eventBus.publish(Constant.LOAD_DATA, "null");
  }

  private void loadSnapshot(Image image) {
    final MutableBoolean isAllDataLoaded = new MutableBoolean(false);
    final FragmentHandler fragmentHandler =
        (buffer, offset, length, header) -> {
          final var protoBytes = new byte[length];
          buffer.getBytes(offset, protoBytes);
          try {
            ORESnapshot oreSnapshot = ORESnapshot.parseFrom(protoBytes);
            eventBus.publish(Constant.LOAD_SNAPSHOT, oreSnapshot);
          } catch (InvalidProtocolBufferException e) {
            throw new GenericException("Error parsing proto bytes", e);
          }
          isAllDataLoaded.set(true);
        };

    FragmentAssembler fragmentAssembler = new FragmentAssembler(fragmentHandler, 1024);
    while (!image.isEndOfStream()) {
      final int fragmentsPolled = image.poll(fragmentAssembler, 100);

      if (isAllDataLoaded.value) {
        break;
      }

      IdelStrategyEnum.YIELD.getIdleStrategy().idle(fragmentsPolled);
    }

    assert image.isEndOfStream();
    assert isAllDataLoaded.value;
  }

  @Override
  public void onSessionOpen(ClientSession clientSession, long l) {
    log.info("SORService Session opened");
  }

  @Override
  public void onSessionClose(ClientSession clientSession, long l, CloseReason closeReason) {
    log.info("SORService Session closed");
  }

  @Override
  public void onSessionMessage(
      ClientSession session,
      long timestamp,
      DirectBuffer buffer,
      int offset,
      int length,
      Header header) {
    log.trace("SORService Session message received");
    final var protoBytes = new byte[length];
    buffer.getBytes(offset, protoBytes);
    try {
      Request request = Request.parseFrom(protoBytes);
      if (request.hasPsOrder()) {
        eventBus.publish(Constant.ORDER, request.getPsOrder());
      } else if (request.hasMatchResponses()
          && request.getMatchResponses().getResponses(0).hasPsChildOrderExecReport()) {
        DeliveryOptions options = new DeliveryOptions();
        options.addHeader("tradeId", IdUtil.formatId(Constant.ID_SEQUENCE.getAndIncrement(), 14));

        long currentSequence = sequenceCache.get(request.getSource());
        if (request.getOutSequence() <= currentSequence) {
          log.trace(
              "ignore exec report, sequence: {}, current sequence: {}",
              request.getOutSequence(),
              currentSequence);
          return;
        }
        sequenceCache.put(request.getSource(), request.getOutSequence());
        eventBus.send(
            Constant.ER,
            request.getMatchResponses().getResponses(0).getPsChildOrderExecReport(),
            options);
      } else if (request.hasMatchResponses()
          && request.getMatchResponses().getResponses(0).hasGetOrderbook()) {
        long currentSequence = sequenceCache.get(request.getSource());
        if (request.getOutSequence() <= currentSequence) {
          log.trace(
              "ignore order book, sequence: {}, current sequence: {}",
              request.getOutSequence(),
              currentSequence);
          return;
        }
        sequenceCache.put(request.getSource(), request.getOutSequence());
        eventBus.publish(Constant.ORDER_BOOK, request);
      } else if (request.hasConfig()) {
        eventBus.publish(Constant.CONFIG_UPDATE, request.getConfig());
      } else if (request.hasSorReject()) {
        eventBus.publish(Constant.REJECTION_INTERNAL, request.getSorReject());
      } else {
        log.error(
            "Unknown request type: {}, length: {}, base64: {}",
            request,
            length,
            Base64.getEncoder().encodeToString(protoBytes));
      }
    } catch (InvalidProtocolBufferException e) {
      String base64 = Base64.getEncoder().encodeToString(protoBytes);
      log.error("Error parsing proto bytes: {}", base64, e);
    } catch (Exception e) {
      log.error("message error", e);
    }
  }

  @Override
  public void onTimerEvent(long l, long l1) {
    log.info("SORService Timer event");
  }

  @Override
  public void onTakeSnapshot(ExclusivePublication exclusivePublication) {
    log.info("SORService Taking snapshot, streamId: {}", exclusivePublication.streamId());
    log.debug("exclusivePublication connection status: {}", exclusivePublication.isConnected());
    List<ORESnapshot.Builder> snapshotBuilders = new ArrayList<>();
    AtomicReference<List<ORESnapshot.Builder>> snapshot = new AtomicReference<>();
    eventBus
        .request(Constant.TAKE_SNAPSHOT, snapshotBuilders)
        .onComplete(
            ar -> {
              if (ar.succeeded()) {
                log.debug("Snapshot written successfully");
                snapshot.set(snapshotBuilders);
              } else {
                snapshot.set(Collections.emptyList());
                log.error("Error taking snapshot", ar.cause());
              }
            });
    while (snapshot.get() == null) {
      idleStrategy.idle();
    }
    sendSnapshot(exclusivePublication, snapshot.get());
  }

  private void sendSnapshot(
      ExclusivePublication exclusivePublication, List<ORESnapshot.Builder> snapshots) {
    log.debug(
        "sending to exclusivePublication, connection status: {}",
        exclusivePublication.isConnected());
    long size = 0;
    for (ORESnapshot.Builder snapshot : snapshots) {
      ORESnapshot oreSnapshot = snapshot.build();
      byte[] snapshotBytes = oreSnapshot.toByteArray();
      AtomicBuffer buffer = new UnsafeBuffer(snapshotBytes);
      buffer.wrap(snapshotBytes);
      idleStrategy.reset();
      int length = snapshotBytes.length;
      if (length > 8388608) {
        String snapshotName = getSnapshotName(snapshot);
        log.error("{} snapshot is too large: {}", snapshotName, oreSnapshot);
        continue;
      }
      while (true) {
        long offer = exclusivePublication.offer(buffer, 0, length);
        if (offer >= 0) {
          size += length;
          break;
        }
        idleStrategy.idle();
        log.error("Failed to send snapshot, offer: {}", offer);
      }
    }
    log.info("Taking snapshot successfully, length: {}", size);
  }

  private static String getSnapshotName(ORESnapshot.Builder snapshot) {
    String snapshotName = null;
    if (snapshot.getOpenOrdersCount() > 0) {
      snapshotName = "openOrders";
    } else if (snapshot.getOrderbooksCount() > 0) {
      snapshotName = "orderbooks";
    } else if (snapshot.getTickerUpdatesCount() > 0) {
      snapshotName = "tickers";
    } else if (snapshot.getVenueMarketUpdatesCount() > 0) {
      snapshotName = "venueMarkets";
    }
    return snapshotName;
  }

  @Override
  public void onRoleChange(Cluster.Role role) {
    log.info("SORService Role changed to {}", role);
    clientManager.changeRole(role);
    eventBus.publish(Constant.ROLE_CHANGE, role);
  }

  @Override
  public void onTerminate(Cluster cluster) {
    log.info("SORService Terminating");
  }
}
