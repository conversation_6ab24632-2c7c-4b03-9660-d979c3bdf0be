package io.hydrax.pricestreaming.aeron.config;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.client.MessageRouter;
import io.hydrax.aeron.common.Topic;
import io.hydrax.aeron.config.AeronClientProperty;
import io.hydrax.pricestreaming.utils.TopicUtil;
import io.quarkus.runtime.Startup;
import io.vertx.core.Vertx;
import jakarta.annotation.Priority;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Singleton;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@Slf4j
@Singleton
@RequiredArgsConstructor
public class AeronConfiguration {

  private final Vertx vertx;

  @Produces
  @Priority(12)
  @Singleton
  @Startup
  public ClientManager clientManager(
      AeronClientProperty aeronClientProperty,
      MessageRouter messageRouter,
      ErrorHandler errorHandler) {
    return new ClientManager(messageRouter, aeronClientProperty, b -> {}, errorHandler::handle);
  }

  @Produces
  @Singleton
  public MessageRouter messageRouter() {
    MessageRouter router = new MessageRouter();
    router.setRules(
        List.of(
            MessageRouter.MessageRouteRule.builder()
                .topic(TopicUtil.create(Topic.ORDER, "SFOX"))
                .targetName("venue-sfox")
                .build(),
            MessageRouter.MessageRouteRule.builder().topic(Topic.ER).targetName("sor-out").build(),
            MessageRouter.MessageRouteRule.builder()
                .topic(Topic.ORDER_BOOK)
                .targetName("market-data-gateway")
                .build(),
            MessageRouter.MessageRouteRule.builder()
                .topic(TopicUtil.create(Topic.ORDER, "LP1"))
                .targetName("venue-lp1")
                .build()));
    return router;
  }
}
