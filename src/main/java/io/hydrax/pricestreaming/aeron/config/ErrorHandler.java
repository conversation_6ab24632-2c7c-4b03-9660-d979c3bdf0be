package io.hydrax.pricestreaming.aeron.config;

import io.hydrax.aeron.AeronMessage;
import io.hydrax.aeron.ConnectionException;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.PlaceOrder;
import io.hydrax.pricestreaming.utils.ExceptionUtil;
import io.hydrax.proto.metwo.match.PsOrder;
import io.hydrax.proto.metwo.match.PsParentOrderExecReport;
import io.vertx.core.eventbus.EventBus;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class ErrorHandler {
  final OrderCache orderCache;
  final EventBus eventBus;

  public void handle(AeronMessage aeronMessage, Throwable throwable) {
    log.error("Error in AeronMessage: {}", aeronMessage, throwable);
    if (aeronMessage instanceof PlaceOrder request) {
      if (request.getRequest().hasPsOrder()
          && throwable instanceof ConnectionException connectionException) {

        Long parentId = orderCache.getParentId(request.getRequest().getPsOrder().getClOrdId());
        PsOrder parentOrder = orderCache.getParentOrder(parentId);
        PsParentOrderExecReport er =
            ExceptionUtil.buildParentOrderExecReport(parentOrder, connectionException.getMessage());
        eventBus.publish(Constant.REJECTION, er);
      }
    } else {
      log.trace("ignore message type: {}", aeronMessage);
    }
  }
}
