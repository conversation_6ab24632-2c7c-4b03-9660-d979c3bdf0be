package io.hydrax.pricestreaming.events;

import static io.hydrax.pricestreaming.common.Constant.*;
import static io.hydrax.pricestreaming.service.FeeService.getPsTradeFeeAndTax;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.config.MarketModelServiceFactory;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.service.MarketModelService;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.pricestreaming.utils.OrderStatusConverter;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.quarkus.runtime.util.StringUtil;
import io.quarkus.vertx.ConsumeEvent;
import io.vertx.core.eventbus.Message;
import jakarta.enterprise.context.ApplicationScoped;
import java.math.BigDecimal;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationScoped
public class ExecutionReportEvent {

  final ClientManager clientManager;

  final OrderCache orderCache;

  final MarketCache marketCache;

  final MarketModelServiceFactory marketModelServiceFactory;

  @ConsumeEvent(ER)
  //  @RunOnVirtualThread
  @SuppressWarnings("unused")
  public void onExecReport(Message<PsChildOrderExecReport> message) {
    PsChildOrderExecReport psChildOrderExecReport = message.body();
    log.trace(
        "[*SMART_ORDER_ROUTER*] onExecReport child Order send exec report: {}",
        psChildOrderExecReport);
    String psTradeId = "T-" + message.headers().get("tradeId");
    String childOrderId = psChildOrderExecReport.getFixRequestId();
    if (StringUtil.isNullOrEmpty(childOrderId)) {
      log.error(
          "Child order id is not found in exec report, exec report: {}", psChildOrderExecReport);
      return;
    }
    Long parentOrderId = orderCache.getParentId(childOrderId);
    if (parentOrderId == null) {
      log.error("Parent order id is not found in cache, childOrderId: {}", childOrderId);
      return;
    }
    PsOrder psOrder = orderCache.getParentOrder(parentOrderId);
    if (psOrder == null) {
      log.error("parent order is not found in cache, parentOrderId: {}", parentOrderId);
      return;
    }

    if (CLOSED_ORDER_STATUS.contains(psChildOrderExecReport.getOrderStatus())) {
      orderCache.removeChild(childOrderId);
      orderCache.removeParent(parentOrderId);
    }
    PsChildOrderExecReport.Builder childOrderExecReportBuilder = psChildOrderExecReport.toBuilder();
    // Step 1: Create a new instance of PsChildOrderExecReport with additional fields populated
    // If the class is immutable, use the Builder pattern to add extra properties
    String marketModel = marketCache.getMarketModelBySymbolCode(psOrder.getSymbol());
    BigDecimal premium = UDec128Util.toBigDecimal(psOrder.getPremium());

    // Calculate the execIncludePremiumAveragePrice
    UDec128 execIncludePremiumAveragePrice =
        getExecIncludePremiumAveragePrice(
            UDec128Util.toBigDecimal(psChildOrderExecReport.getExecAveragePrice()),
            premium,
            psOrder.getSide());
    MarketModelService modelService = marketModelServiceFactory.get(marketModel);
    UDec128 lastFillQuantity = psChildOrderExecReport.getLastFillQuantity();
    FeeResponse feesResp = psOrder.getHxFeesResp();

    UDec128 remainingEarmarkFee = UDec128Util.ZERO;
    UDec128 remainingEarmarkTax = UDec128Util.ZERO;

    UDec128 tradeFeeAmount = UDec128Util.ZERO;
    UDec128 tradeTaxAmount = UDec128Util.ZERO;
    String parentOrderIdStr = "P-" + IdUtil.formatId(parentOrderId, 12);

    if (psChildOrderExecReport.getPsExecType() == PsExecType.PS_EXEC_TYPE_TRADE) {
      log.trace(
          "[*SMART_ORDER_ROUTER*] onExecReport send trade exec report, trade exec report: {}",
          psChildOrderExecReport);
      childOrderExecReportBuilder.setPsTradeId(psTradeId);
      BigDecimal lastFillPrice =
          UDec128Util.toBigDecimal(psChildOrderExecReport.getLastFillPrice());
      childOrderExecReportBuilder.setPremium(UDec128Util.from(premium));
      if (lastFillPrice.compareTo(BigDecimal.ZERO) > 0) {
        UDec128 includePremiumPrice;
        if (childOrderExecReportBuilder.getSide() == Side.SIDE_SELL) {
          includePremiumPrice = UDec128Util.from(lastFillPrice.subtract(premium));
          childOrderExecReportBuilder.setIncludePremiumPrice(includePremiumPrice);
        } else {
          includePremiumPrice = UDec128Util.from(lastFillPrice.add(premium));
          childOrderExecReportBuilder.setIncludePremiumPrice(includePremiumPrice);
        }
      }
      // Calculate the trade fees
      Map<String, UDec128> remainingEarmarkData =
          orderCache.getParentOrderRemainingEarmark(parentOrderId);
      remainingEarmarkFee = remainingEarmarkData.get("remainingEarmarkFee");
      remainingEarmarkTax = remainingEarmarkData.get("remainingEarmarkTax");

      log.debug(
          "[*SMART_ORDER_ROUTER*] onExecReport calculate earmark, parentOrderId: {},"
              + " remainingEarmarkFee: {}, remainingEarmarkTax: {},",
          parentOrderIdStr,
          remainingEarmarkFee,
          remainingEarmarkTax);

      if (!UDec128Util.isZero(remainingEarmarkFee) || !UDec128Util.isZero(remainingEarmarkTax)) {
        UDec128 tradeAmount =
            UDec128Util.multiply(lastFillQuantity, execIncludePremiumAveragePrice);

        Map<String, UDec128> psTradeFeeAndTax =
            getPsTradeFeeAndTax(
                childOrderId,
                feesResp,
                lastFillQuantity,
                tradeAmount,
                remainingEarmarkFee,
                remainingEarmarkTax);

        tradeFeeAmount = psTradeFeeAndTax.get("releaseEarmarkFee");
        tradeTaxAmount = psTradeFeeAndTax.get("releaseEarmarkTax");

        remainingEarmarkFee = psTradeFeeAndTax.get("remainingEarmarkFee");
        remainingEarmarkTax = psTradeFeeAndTax.get("remainingEarmarkTax");
        log.debug(
            "[*SMART_ORDER_ROUTER*] onExecReport calculate trade result, parentOrderId: {},"
                + " tradeFeeAmount: {}, tradeTaxAmount: {}, remainingEarmarkFee: {},"
                + " remainingEarmarkTax: {}",
            parentOrderId,
            tradeFeeAmount,
            tradeTaxAmount,
            remainingEarmarkFee,
            remainingEarmarkTax);

        orderCache.putParentOrderRemainingEarmark(
            parentOrderId, "remainingEarmarkFee", remainingEarmarkFee);
        orderCache.putParentOrderRemainingEarmark(
            parentOrderId, "remainingEarmarkTax", remainingEarmarkTax);
        if (childOrderExecReportBuilder.getOrderStatus()
            == PsChildOrderStatus.PS_CHILD_ORDER_STATUS_CONFIRMED) {
          orderCache.removeParentOrderRemainingEarmark(parentOrderId);
        }
      }
    }
    TradingVenueAccountDTO tradingVenueAccount =
        modelService.getTradingVenueAccount(psChildOrderExecReport.getVenueCode(), psOrder);
    psChildOrderExecReport =
        childOrderExecReportBuilder
            .setOrderId(childOrderId)
            .setParentOrderId(parentOrderIdStr)
            .setUserId(psOrder.getUserId())
            .setSymbol(psOrder.getSymbol())
            .setAssetHoldingAccountId(
                tradingVenueAccount == null
                    ? null
                    : tradingVenueAccount.getAssetHoldingAccountCode())
            .setMemberAssetHoldingAccountId(psOrder.getHoldingAccountId())
            .setSide(psOrder.getSide())
            .setMarketModel(marketModel)
            .setTimeInForce(psOrder.getTimeInForce())
            .setPsOrderType(psOrder.getOrdType())
            .setFromService("sor")
            .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_LP)
            .setTradeFeeAmount(tradeFeeAmount)
            .setTradeTaxAmount(tradeTaxAmount)
            .build();
    //    log.trace(
    //        "[*SMART_ORDER_ROUTER*] onExecReport send child order exec report: {}",
    //        psChildOrderExecReport);
    //    clientManager.send(
    //        ERResponseList.builder()
    //            .responseList(
    //                ResponseList.newBuilder()
    //                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
    //                    .addResponses(
    //                        Response.newBuilder()
    //                            .setPsChildOrderExecReport(psChildOrderExecReport)
    //                            .build())
    //                    .build())
    //            .topic(Topic.ER)
    //            .build());
    //    log.trace(
    //        "[*SMART_ORDER_ROUTER*] onExecReport send child order exec report done, childOrderId:
    // {}",
    //        childOrderId);
    // Step 2: Send an execution report update for the parent order
    // TODO: In the future, the status of the parent order should be updated based on the statuses
    // of multiple child orders.
    log.debug(
        "[*SMART_ORDER_ROUTER*] onExecReport remainingEarmarkFee: {}, remainingEarmarkTax: {},"
            + " tradeFeeAmount: {}, tradeTaxAmount: {},",
        remainingEarmarkFee,
        remainingEarmarkTax,
        tradeFeeAmount,
        tradeTaxAmount);
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(
                                PsParentOrderExecReport.newBuilder()
                                    .setOrderId(parentOrderIdStr)
                                    .setExternalOrderId(psOrder.getClOrdId())
                                    .setSymbol(psOrder.getSymbol())
                                    .setUserId(psOrder.getUserId())
                                    .setFromService("sor")
                                    .setPsSenderService(PsSenderService.PS_SENDER_SERVICE_LP)
                                    .setPsExecType(psChildOrderExecReport.getPsExecType())
                                    .setAssetHoldingAccountId(psOrder.getHoldingAccountId())
                                    .setServiceAccountId(psOrder.getServiceAccountId())
                                    .setMarketCode(psOrder.getMarketCode())
                                    .setEarmarkAmt(psOrder.getEarmarkAmt())
                                    .setTimeInForce(psOrder.getTimeInForce())
                                    .setPsOrderType(psOrder.getOrdType())
                                    .setSide(psOrder.getSide())
                                    .setPrice(psOrder.getPrice())
                                    .setQuantity(psOrder.getQty())
                                    .setExecIncludePremiumAveragePrice(
                                        execIncludePremiumAveragePrice)
                                    .setLastTradeId(psChildOrderExecReport.getPsTradeId())
                                    .setBaseBalanceAccountId(psOrder.getBaseBalanceAccountId())
                                    .setQuoteBalanceAccountId(psOrder.getQuoteBalanceAccountId())
                                    .setLastFillPrice(psChildOrderExecReport.getLastFillPrice())
                                    .setLastFillQuantity(lastFillQuantity)
                                    .setCumulativeFillAmount(
                                        psChildOrderExecReport.getCumulativeFillAmount())
                                    .setCumulativeFillQuantity(
                                        psChildOrderExecReport.getCumulativeFillAmount())
                                    .setIncludePremiumPrice(
                                        psChildOrderExecReport.getIncludePremiumPrice())
                                    .setProcessedTime(System.currentTimeMillis())
                                    .setOrderStatus(
                                        OrderStatusConverter.convertToOrderStatus(
                                            psChildOrderExecReport.getOrderStatus()))
                                    .setPremium(UDec128Util.from(premium))
                                    .setError(psChildOrderExecReport.getError())
                                    .setFeeResponse(feesResp)
                                    .setFeeRate(psOrder.getFeeRate())
                                    .setTaxRate(psOrder.getTaxRate())
                                    .setEstimatedFees(psOrder.getEarmarkFeeAmt())
                                    .setEstimatedTax(psOrder.getEarmarkTaxAmt())
                                    .setRemainingEarmarkFee(remainingEarmarkFee)
                                    .setRemainingEarmarkTax(remainingEarmarkTax)
                                    .setTradeFeeAmount(tradeFeeAmount)
                                    .setTradeTaxAmount(tradeTaxAmount)
                                    .build())
                            .build())
                    .addResponses(
                        Response.newBuilder()
                            .setPsChildOrderExecReport(psChildOrderExecReport)
                            .build())
                    .build())
            .topic(Topic.ER)
            .build());
    log.trace(
        "[*SMART_ORDER_ROUTER*] onExecReport send parent order exec report done, parentOrderId: {}",
        parentOrderIdStr);
  }

  private static UDec128 getExecIncludePremiumAveragePrice(
      BigDecimal execAveragePrice, BigDecimal premium, Side side) {
    UDec128 execIncludePremiumAveragePrice;
    if (side == Side.SIDE_SELL) {
      execIncludePremiumAveragePrice = UDec128Util.from(execAveragePrice.subtract(premium));
    } else {
      execIncludePremiumAveragePrice = UDec128Util.from(execAveragePrice.add(premium));
    }
    return execIncludePremiumAveragePrice;
  }
}
