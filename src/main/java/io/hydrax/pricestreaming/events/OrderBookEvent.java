package io.hydrax.pricestreaming.events;

import static io.hydrax.pricestreaming.common.Constant.*;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.MarketCache;
import io.hydrax.pricestreaming.cache.TradingVenueCache;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.PriceLevelWithVenue;
import io.hydrax.pricestreaming.service.OrderBookService;
import io.hydrax.proto.metwo.match.GetOrderbookResp;
import io.hydrax.proto.metwo.match.PriceUpdate;
import io.hydrax.proto.metwo.match.Request;
import io.hydrax.proto.metwo.match.Response;
import io.hydrax.proto.metwo.match.ResponseList;
import io.hydrax.proto.metwo.match.Side;
import io.quarkus.vertx.ConsumeEvent;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationScoped
public class OrderBookEvent {

  final OrderBookService orderBookService;
  final ClientManager clientManager;
  final MarketCache marketCache;
  final TradingVenueCache tradingVenueCache;

  @ConsumeEvent(Constant.ORDER_BOOK)
  @SuppressWarnings("unused")
  public void onOrderBook(Request request) {
    log.trace("OrderBookEvent: {}", request);
    request.getMatchResponses().getResponsesList().stream()
        .filter(Response::hasGetOrderbook)
        .map(Response::getGetOrderbook)
        .forEach(
            orderbook -> {
              if (orderbook.getOnlyTrades()) {
                // Send price update event if only_trades is true
                String source = request.getSource();
                sendPriceUpdateEvent(orderbook, source);
              } else {
                log.trace("OrderBookEvent order book update: {}", request);
                orderBookService.update(
                    request.getSource(),
                    orderbook.getSymbol(),
                    orderbook.getLastTradeTime(),
                    orderbook.getLastTradePrice(),
                    orderbook.getLastTradeQty(),
                    orderbook.getAsksList().stream()
                        .map(p -> PriceLevelWithVenue.from(p, request.getSource()))
                        .toList(),
                    Side.SIDE_BUY);
                orderBookService.update(
                    request.getSource(),
                    orderbook.getSymbol(),
                    orderbook.getLastTradeTime(),
                    orderbook.getLastTradePrice(),
                    orderbook.getLastTradeQty(),
                    orderbook.getBidsList().stream()
                        .map(p -> PriceLevelWithVenue.from(p, request.getSource()))
                        .toList(),
                    Side.SIDE_SELL);
              }
            });
  }

  private void sendPriceUpdateEvent(GetOrderbookResp orderbook, String source) {
    log.trace(
        "Starting price update for symbol: {}, orderbook: {}, source: {}",
        orderbook.getSymbol(),
        orderbook,
        source);
    // Retrieve the venue symbol and corresponding symbol codes
    String venueSymbol = orderbook.getSymbol();
    List<String> symbolCodes =
        tradingVenueCache.getSymbolCodesByVenueMarketAndVenueSymbol(source, venueSymbol);

    if (symbolCodes.isEmpty()) {
      log.warn("Symbol codes not found for venueSymbol: {}, source: {}", venueSymbol, source);
      return;
    }

    // Prepare responses for all symbol codes
    List<Response> responses = new ArrayList<>();
    for (String symbolCode : symbolCodes) {
      PriceUpdate.Builder priceUpdateBuilder = PriceUpdate.newBuilder();
      priceUpdateBuilder.setSymbol(symbolCode);
      priceUpdateBuilder.setLastTradeQty(orderbook.getLastTradeQty());
      priceUpdateBuilder.setLastTradePrice(orderbook.getLastTradePrice());
      priceUpdateBuilder.setLastTradeTime(
          orderbook.getLastTradeTime() * 1_000_000); // Convert to nanoseconds
      priceUpdateBuilder.setFromService("sor");
      String marketTimezone = marketCache.getMarketTimezoneBySymbolCode(symbolCode);
      if (marketTimezone == null) {
        log.warn("Timezone not found for symbolCode: {}", symbolCode);
        return;
      }
      priceUpdateBuilder.setTimezone(marketTimezone);

      responses.add(Response.newBuilder().setPriceUpdate(priceUpdateBuilder).build());
    }

    // Send all price updates via the client manager
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .setFromService("sor")
                    .setOutSequence(PRICE_UPDATE_SEQUENCE.getAndIncrement())
                    .addAllResponses(responses)
                    .build())
            .topic(Topic.ER)
            .build());

    // Log the completion of the price update
    log.trace("Price update sent for {} symbols. responses: {}", symbolCodes.size(), responses);
  }
}
