package io.hydrax.pricestreaming.events;

import static io.hydrax.pricestreaming.common.Constant.OUT_SEQUENCE;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.aeron.common.Topic;
import io.hydrax.pricestreaming.cache.OrderCache;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.ERResponseList;
import io.hydrax.pricestreaming.domain.RejectionDTO;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.proto.metwo.match.PsParentOrderExecReport;
import io.hydrax.proto.metwo.match.Response;
import io.hydrax.proto.metwo.match.ResponseList;
import io.hydrax.proto.metwo.match.SORReject;
import io.quarkus.vertx.ConsumeEvent;
import jakarta.inject.Singleton;
import java.util.Map;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Singleton
public class RejectionEvent {

  final ClientManager clientManager;
  final OrderCache orderCache;

  @ConsumeEvent(Constant.REJECTION)
  @SuppressWarnings("unused")
  public void onRejection(PsParentOrderExecReport er) {
    RejectionDTO sorReject =
        RejectionDTO.builder()
            .sorReject(SORReject.newBuilder().setParentOrderExecReport(er).build())
            .build();
    clientManager.sendToOwn(sorReject);
  }

  @ConsumeEvent(Constant.REJECTION_INTERNAL)
  @SuppressWarnings("unused")
  public void onRejectionInternal(SORReject sorReject) {
    Long parentId = IdUtil.formatId(sorReject.getParentOrderExecReport().getOrderId());
    orderCache.removeParent(parentId);
    orderCache.getChildParentCache().entrySet().stream()
        .filter(entry -> entry.getValue().equals(parentId))
        .map(Map.Entry::getKey)
        .forEach(orderCache::removeChild);
    clientManager.send(
        ERResponseList.builder()
            .responseList(
                ResponseList.newBuilder()
                    .addResponses(
                        Response.newBuilder()
                            .setPsParentOrderExecReport(sorReject.getParentOrderExecReport())
                            .build())
                    .setOutSequence(OUT_SEQUENCE.getAndIncrement())
                    .build())
            .topic(Topic.ER)
            .build());
  }
}
