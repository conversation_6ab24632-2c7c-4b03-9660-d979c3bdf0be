package io.hydrax.pricestreaming.events;

import com.google.protobuf.ByteString;
import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.*;
import io.hydrax.pricestreaming.domain.TickerDTO;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.proto.metwo.match.*;
import io.quarkus.vertx.ConsumeEvent;
import io.smallrye.common.annotation.Blocking;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class SnapshotEvent {
  final OrderBookCache orderBookCache;
  final TradingVenueCache tradingVenueCache;
  final TradingVenueAccountCache tradingVenueAccountCache;
  final OrderRoutingStrategyCache orderRoutingStrategyCache;
  final OrderCache orderCache;
  final SequenceVenueCache sequenceVenueCache;
  final TickerCache tickerCache;
  final MarketCache marketCache;
  final SequenceCache sequenceCache;

  @ConsumeEvent(Constant.LOAD_SNAPSHOT)
  @SuppressWarnings("unused")
  public void onLoadSnapshot(ORESnapshot snapshot) {
    log.debug("loading snapshot: {}", snapshot);
    if (snapshot.getSequence() > 1) {
      Constant.OUT_SEQUENCE.set(snapshot.getSequence());
    }
    if (snapshot.getIdSequence() > 1) {
      Constant.ID_SEQUENCE.set(snapshot.getIdSequence());
    }
    if (snapshot.getPriceUpdateSequence() > 1) {
      Constant.PRICE_UPDATE_SEQUENCE.set(snapshot.getPriceUpdateSequence());
    }
    if (snapshot.getTradeVenueSequencesCount() > 0) {
      snapshot
          .getTradeVenueSequencesList()
          .forEach(s -> sequenceCache.put(s.getVenueCode(), s.getSequence()));
    }
    if (snapshot.getVenueAccountUpdatesCount() > 0) {
      snapshot
          .getVenueAccountUpdatesList()
          .forEach(a -> tradingVenueAccountCache.put(TradingVenueAccountDTO.from(a)));
      log.debug("load TradingVenueAccount: {}", tradingVenueAccountCache.getAll());
    }
    if (snapshot.getVenueMarketUpdatesCount() > 0) {
      snapshot.getVenueMarketUpdatesList().forEach(tradingVenueCache::put);
      tradingVenueCache
          .getAll()
          .forEach(
              m ->
                  orderBookCache.updateOrderBookKey(tradingVenueCache.getAll(), m.getMarketCode()));
      log.debug("load TradingVenue: {}", tradingVenueCache.getAll());
    }
    if (snapshot.getOrderRoutingStrategyUpdatesCount() > 0) {
      snapshot
          .getOrderRoutingStrategyUpdatesList()
          .forEach(
              s -> {
                sequenceVenueCache.put(
                    s.getCode(),
                    s.getSequenceVenueMarketsList().stream()
                        .sorted(Comparator.comparing(SequenceVenueMarket::getSequence))
                        .map(SequenceVenueMarket::getVenueMarketCode)
                        .toList());
                orderRoutingStrategyCache.put(s.getCode(), OrderRoutingStrategyDTO.from(s));
              });
      log.debug("load OrderRoutingStrategy: {}", orderRoutingStrategyCache.getAll());
    }
    if (snapshot.getOpenOrdersCount() > 0) {
      snapshot.getOpenOrdersList().forEach(o -> orderCache.put(IdUtil.formatId(o.getOrderId()), o));
      log.debug("load OpenOrders: {}", orderCache.getAllParentOrder());
    }
    if (snapshot.getChildOrderParentOrderSnapshotsCount() > 0) {
      snapshot
          .getChildOrderParentOrderSnapshotsList()
          .forEach(
              c -> {
                orderCache.put(c.getChildOrderId(), c.getParentOrderId());
                orderCache.putChildOrderList(c.getParentOrderId(), List.of(c.getChildOrderId()));
                orderCache.putChildOrder(c.getChildOrderId(), c.getChildOrderSenderService());
              });
    }
    //    if (!snapshot.getMostOrderIds().isEmpty()) {
    //      try {
    //        orderCache.initOrderIds(snapshot.getMostOrderIds().toByteArray());
    //      } catch (IOException e) {
    //        log.error("Error deserializing most order ids", e);
    //      }
    //      log.debug("load most order ids");
    //    }
    if (snapshot.getOrderbooksCount() > 0) {
      Map<String, List<OREOrderBookSnapshot>> orderbookMap =
          snapshot.getOrderbooksList().stream()
              .collect(Collectors.groupingBy(OREOrderBookSnapshot::getSymbol));
      orderbookMap.forEach(
          (symbol, orderBookSnapshots) ->
              orderBookSnapshots.stream()
                  .map(this::from)
                  .forEach(orderBook -> orderBookCache.put(symbol, orderBook)));
      log.debug("load order books: {}", orderBookCache.getOrderBooks());
    }
    if (snapshot.getTickerUpdatesCount() > 0) {
      snapshot
          .getTickerUpdatesList()
          .forEach(
              t -> {
                TickerDTO tickerDTO = TickerDTO.from(t);
                if (tickerDTO != null) {
                  tickerCache.put(tickerDTO);
                } else {
                  log.warn("ticker cannot transfer: {}", t);
                }
              });
      log.debug("load ticker updates: {}", tickerCache.getAll());
    }
    if (snapshot.getMarketUpdatesCount() > 0) {
      snapshot
          .getMarketUpdatesList()
          .forEach(
              m -> {
                MarketDTO marketDTO = MarketDTO.from(m);
                if (marketDTO != null) {
                  marketCache.put(marketDTO);
                } else {
                  log.warn("market cannot transfer: {}", m);
                }
              });
      log.debug("load market updates: {}", marketCache.getAll());
    }

    if (!snapshot.getParentOrderRemainingEarmarkList().isEmpty()) {
      orderCache.loadParentOrderRemainingEarmark(snapshot.getParentOrderRemainingEarmarkList());
    }
  }

  private OrderBook from(OREOrderBookSnapshot orderBookSnapshot) {
    Map<BigDecimal, List<PriceLevelWithVenue>> asks =
        orderBookSnapshot.getAskLevelsList().stream()
            .map(PriceLevelWithVenue::from)
            .collect(Collectors.groupingBy(PriceLevelWithVenue::getPrice));
    Map<BigDecimal, List<PriceLevelWithVenue>> bids =
        orderBookSnapshot.getBidLevelsList().stream()
            .map(PriceLevelWithVenue::from)
            .collect(Collectors.groupingBy(PriceLevelWithVenue::getPrice));

    return new TreeMapOrderBook(
        orderBookSnapshot.getLastTradeTime(),
        orderBookSnapshot.getLastTradePrice(),
        orderBookSnapshot.getLastTradeQty(),
        bids,
        asks);
  }

  @ConsumeEvent(Constant.TAKE_SNAPSHOT)
  @Blocking
  @SuppressWarnings("unused")
  public List<ORESnapshot.Builder> onTakeSnapshot(final List<ORESnapshot.Builder> builders) {

    log.debug("write Out Sequence: {}", Constant.OUT_SEQUENCE.get());
    saveSequence(builders);
    log.debug("taking snapshot");
    log.debug("write TradingVenue: {}", tradingVenueCache.getAll());
    saveVenueMarket(builders);

    log.debug("write TradingVenueAccount: {}", tradingVenueAccountCache.getAll());
    saveVenueAccount(builders);

    log.debug("write OrderRoutingStrategy: {}", orderRoutingStrategyCache.getAll());
    saveOrderStrategy(builders);

    log.debug("write open orders: {}", orderCache.getAllParentOrder());
    saveOpenOrder(builders);

    saveChildParentOrder(builders);
    //    saveMostOrderIds(builders);
    saveOrderBook(builders);

    saveTickers(builders);
    saveMarkets(builders);

    saveParentOrderRemainingEarmark(builders);
    return builders;
  }

  private void saveTickers(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllTickerUpdates(
        tickerCache.getAll().stream().map(TickerDTO::toProto).filter(Objects::nonNull).toList());
    log.debug("write tickers: {}", builder.getTickerUpdatesList());
    builders.add(builder);
  }

  private void saveMarkets(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllMarketUpdates(
        marketCache.getAll().stream().map(MarketDTO::toProto).filter(Objects::nonNull).toList());
    log.debug("write markets: {}", builder.getMarketUpdatesList());
    builders.add(builder);
  }

  private void saveOrderBook(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllOrderbooks(
        orderBookCache.getOrderBooks().entrySet().stream()
            .map(e -> this.toProto(e.getValue(), e.getKey()))
            .toList());
    log.debug("write order book: {}", builder.getOrderbooksList());
    builders.add(builder);
  }

  private void saveMostOrderIds(List<ORESnapshot.Builder> builders) {
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    try {
      orderCache.geOrderIds(byteArrayOutputStream);
      ORESnapshot.Builder builder = ORESnapshot.newBuilder();
      builder.setMostOrderIds(ByteString.copyFrom(byteArrayOutputStream.toByteArray()));
      builders.add(builder);
      log.debug("write order ids");
    } catch (IOException e) {
      log.error("Error serializing order ids", e);
    }
  }

  private void saveOpenOrder(List<ORESnapshot.Builder> builders) {
    List<PsOrder> allParentOrders = orderCache.getAllParentOrder();
    int batchSize = 500;
    for (int i = 0; i < allParentOrders.size(); i += batchSize) {
      int end = Math.min(i + batchSize, allParentOrders.size());
      List<PsOrder> batch = allParentOrders.subList(i, end);
      ORESnapshot.Builder builder = ORESnapshot.newBuilder();
      builder.addAllOpenOrders(batch);
      builders.add(builder);
    }
  }

  private void saveVenueMarket(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllVenueMarketUpdates(
        tradingVenueCache.getAll().stream().map(TradingVenueDTO::toProto).toList());
    builders.add(builder);
  }

  private void saveVenueAccount(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllVenueAccountUpdates(
        tradingVenueAccountCache.getAll().stream().map(TradingVenueAccountDTO::toProto).toList());
    builders.add(builder);
  }

  private void saveOrderStrategy(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllOrderRoutingStrategyUpdates(
        orderRoutingStrategyCache.getAll().stream()
            .map(
                a ->
                    OrderRoutingStrategyDTO.toProto(
                        (OrderRoutingStrategyDTO) a, sequenceVenueCache.get(a.getCode())))
            .toList());
    builders.add(builder);
  }

  private void saveSequence(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.setSequence(Constant.OUT_SEQUENCE.get());
    builder.setIdSequence(Constant.ID_SEQUENCE.get());
    builder.setPriceUpdateSequence(Constant.PRICE_UPDATE_SEQUENCE.get());
    Map<String, Long> allSequence = sequenceCache.getAll();
    if (!allSequence.isEmpty()) {
      builder.addAllTradeVenueSequences(
          allSequence.entrySet().stream()
              .map(
                  e ->
                      TradeVenueSequence.newBuilder()
                          .setVenueCode(e.getKey())
                          .setSequence(e.getValue())
                          .build())
              .toList());
    }
    builders.add(builder);
  }

  private void saveChildParentOrder(List<ORESnapshot.Builder> builders) {
    List<ChildOrderParentOrderSnapshot> childOrderParentOrderSnapshots =
        orderCache.getChildParentCache().keySet().stream()
            .map(
                k ->
                    ChildOrderParentOrderSnapshot.newBuilder()
                        .setChildOrderId(k)
                        .setParentOrderId(orderCache.getParentId(k))
                        .setChildOrderSenderService(
                            Optional.ofNullable(orderCache.getChildOrder(k)).orElse(""))
                        .build())
            .toList();
    log.debug("write child patent id: {}", childOrderParentOrderSnapshots);
    int batchSize = 1000;
    for (int i = 0; i < childOrderParentOrderSnapshots.size(); i += batchSize) {
      int end = Math.min(i + batchSize, childOrderParentOrderSnapshots.size());
      List<ChildOrderParentOrderSnapshot> batch = childOrderParentOrderSnapshots.subList(i, end);
      ORESnapshot.Builder builder = ORESnapshot.newBuilder();
      builder.addAllChildOrderParentOrderSnapshots(batch);
      builders.add(builder);
    }
  }

  private OREOrderBookSnapshot toProto(OrderBook orderBook, String symbol) {
    return OREOrderBookSnapshot.newBuilder()
        .setSymbol(symbol)
        .setLastTradeTime(orderBook.getLastUpdate())
        .setLastTradePrice(orderBook.getLastTradePrice())
        .setLastTradeQty(orderBook.getLastTradeQty())
        .setLastTradeTime(orderBook.getLastUpdate())
        .addAllAskLevels(
            orderBook.getAsks().stream().map(PriceLevelWithVenue::toSnapshotProto).toList())
        .addAllBidLevels(
            orderBook.getBids().stream().map(PriceLevelWithVenue::toSnapshotProto).toList())
        .build();
  }

  private void saveParentOrderRemainingEarmark(List<ORESnapshot.Builder> builders) {
    ORESnapshot.Builder builder = ORESnapshot.newBuilder();
    builder.addAllParentOrderRemainingEarmark(orderCache.getAllParentOrderRemainingEarmark());
    log.debug(
        "write parentOrderRemainingEarmark: {}", builder.getParentOrderRemainingEarmarkList());
    builders.add(builder);
  }
}
