package io.hydrax.pricestreaming.events;

import io.aeron.Aeron;
import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.client.RecordingSignalConsumer;
import io.aeron.archive.codecs.RecordingSignal;
import io.aeron.archive.status.RecordingPos;
import io.aeron.cluster.ConsensusModule;
import io.aeron.cluster.RecordingLog;
import io.aeron.samples.archive.RecordingDescriptor;
import io.aeron.samples.archive.RecordingDescriptorCollector;
import io.hydrax.aeron.config.AeronClientProperty;
import io.quarkus.runtime.Startup;
import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.agrona.collections.MutableBoolean;

@ApplicationScoped
@Slf4j
@Startup
@RequiredArgsConstructor
public class SystemEvent {
  final AeronClientProperty aeronClientProperty;

  private static String readOutput(Process process) throws IOException {
    BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
    String line;
    StringBuilder output = new StringBuilder();
    while ((line = reader.readLine()) != null) {
      output.append(line).append(System.lineSeparator());
    }
    return output.toString();
  }

  @Scheduled(every = "${snapshot-interval:5m}", delay = 5)
  public void scheduledTask() throws InterruptedException {
    log.info("Scheduled task executed");
    takeSnapshot();
  }

  @Scheduled(every = "${save-position-interval:2h}", delayed = "${save-position-interval:2h}")
  public void purge() {
    log.info("purging");
    final RecordingDescriptorCollector collector = new RecordingDescriptorCollector(10);
    final RecordingLog.Entry latestSnapshot;
    final long recordingId;
    try (RecordingLog recordingLog =
        new RecordingLog(new File(aeronClientProperty.cluster().directory()), false)) {
      latestSnapshot =
          Objects.requireNonNull(
              recordingLog.getLatestSnapshot(ConsensusModule.Configuration.SERVICE_ID));
      recordingId = recordingLog.findLastTermRecordingId();
      if (RecordingPos.NULL_RECORDING_ID == recordingId) {
        log.error("Unable to find log recording");
      }
      try (Aeron aeron =
          Aeron.connect(new Aeron.Context().aeronDirectoryName(aeronClientProperty.directory()))) {
        final MutableBoolean segmentsDeleted = new MutableBoolean(false);
        final RecordingSignalConsumer deleteSignalConsumer =
            (controlSessionId, correlationId, recordingId1, subscriptionId, position, signal) -> {
              if (RecordingSignal.DELETE == signal) {
                segmentsDeleted.set(true);
              }
            };

        final AeronArchive.Context aeronArchiveCtx =
            new AeronArchive.Context()
                .aeron(aeron)
                .controlRequestChannel(aeronClientProperty.archive().controlRequestChannel())
                .controlResponseChannel(aeronClientProperty.archive().controlRequestChannel())
                .recordingSignalConsumer(deleteSignalConsumer)
                .ownsAeronClient(false);

        try (AeronArchive aeronArchive = AeronArchive.connect(aeronArchiveCtx)) {
          aeronArchive.listRecording(recordingId, collector.reset());
          final RecordingDescriptor recordingDescriptor = collector.descriptors().get(0);

          final long newStartPosition =
              AeronArchive.segmentFileBasePosition(
                  recordingDescriptor.startPosition(),
                  latestSnapshot.logPosition,
                  recordingDescriptor.termBufferLength(),
                  recordingDescriptor.segmentFileLength());
          final long lowerBound =
              AeronArchive.segmentFileBasePosition(
                      recordingDescriptor.startPosition(),
                      recordingDescriptor.startPosition(),
                      recordingDescriptor.termBufferLength(),
                      recordingDescriptor.segmentFileLength())
                  + recordingDescriptor.segmentFileLength();
          if (newStartPosition < lowerBound) {
            log.debug(
                "latestSnapshot logPosition:{} ,NewStartPosition {} is less than lower bound {}, skip purging",
                latestSnapshot.logPosition,
                newStartPosition,
                lowerBound);
            return;
          }
          final long segmentsDeleteCount =
              aeronArchive.purgeSegments(recordingId, newStartPosition);

          while (0 < segmentsDeleteCount && !segmentsDeleted.get()) {
            aeronArchive.pollForRecordingSignals();
          }
        }
      }
    }
  }

  public void takeSnapshot() throws InterruptedException {
    try {
      Process process =
          new ProcessBuilder(
                  "java",
                  "-cp",
                  "/aeron/aeron-all-1.43.0.jar",
                  "io.aeron.cluster.ClusterTool",
                  aeronClientProperty.cluster().directory(),
                  "snapshot")
              .start();
      String output = readOutput(process);
      int exitCode = process.waitFor();
      if (exitCode != 0) {
        if (output.contains("Current node is not the leader")) {
          log.warn("Failed to take snapshot. Exit code: {}, result: {}", exitCode, output);
        } else {
          log.error("Failed to take snapshot. Exit code: {}, result: {}", exitCode, output);
        }
      } else {
        log.info("Snapshot output: {}", output);
      }
    } catch (IOException e) {
      log.error("Failed to take snapshot", e);
    }
  }
}
