package io.hydrax.pricestreaming.events;

import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.*;
import io.hydrax.pricestreaming.domain.TickerDTO;
import io.hydrax.proto.metwo.match.*;
import io.quarkus.vertx.ConsumeEvent;
import io.smallrye.common.annotation.Blocking;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Comparator;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class ConfigUpdateEvent {

  final TradingVenueCache tradingVenueCache;
  final TradingVenueAccountCache tradingVenueAccountCache;
  final OrderRoutingStrategyCache orderRoutingStrategyCache;
  final TickerCache tickerCache;
  final MarketCache marketCache;
  final SequenceVenueCache sequenceVenueCache;
  final OrderBookCache orderBookCache;

  static final String INACTIVE = "inactive";

  @ConsumeEvent(Constant.CONFIG_UPDATE)
  @Blocking
  @SuppressWarnings("unused")
  public void onConfigUpdate(ConfigReq config) {
    log.debug("SORService onConfigUpdate: {}", config);
    VenueMarketUpdateRequest venueMarketUpdate = config.getVenueMarketUpdate();
    if (config.hasVenueMarketUpdate()) {
      if (INACTIVE.equals(venueMarketUpdate.getStatus())) {
        tradingVenueCache.remove(venueMarketUpdate);
      } else {
        tradingVenueCache.put(venueMarketUpdate);
      }
      orderBookCache.updateOrderBookKey(
          tradingVenueCache.getAll(), venueMarketUpdate.getMarketCode());
      log.debug("SORService onConfigUpdate: {}, venue_market_update success", config);
    } else if (config.hasOrderRoutingStrategyUpdate()) {
      OrderRoutingStrategyUpdateRequest strategyUpdate = config.getOrderRoutingStrategyUpdate();
      orderRoutingStrategyCache.put(
          strategyUpdate.getCode(), OrderRoutingStrategyDTO.from(strategyUpdate));
      List<String> venueCodes =
          strategyUpdate.getSequenceVenueMarketsList().stream()
              .sorted(Comparator.comparing(SequenceVenueMarket::getSequence))
              .map(SequenceVenueMarket::getVenueMarketCode)
              .toList();
      sequenceVenueCache.put(strategyUpdate.getCode(), venueCodes);
      log.debug("SORService onConfigUpdate: {}, order_routing_strategy_update success", config);

    } else if (config.hasVenueAccountUpdate()) {
      tradingVenueAccountCache.put(TradingVenueAccountDTO.from(config.getVenueAccountUpdate()));
      log.debug("SORService onConfigUpdate: {}, venue_account_update success", config);
    } else if (config.hasTickerUpdate()) {
      TickerDTO tickerDTO = TickerDTO.from(config.getTickerUpdate());
      if (tickerDTO != null) {
        tickerCache.put(tickerDTO);
      } else {
        log.warn("ticker cannot transfer: {}", config.hasTickerUpdate());
      }
    } else if (config.hasMarketUpdate()) {
      MarketDTO marketDTO = MarketDTO.from(config.getMarketUpdate());
      if (marketDTO != null) {
        marketCache.put(marketDTO);
      } else {
        log.warn("market cannot transfer: {}", config.hasMarketUpdate());
      }
      log.debug("SORService onConfigUpdate: {}, marketUpdate success", config);
    }
  }
}
