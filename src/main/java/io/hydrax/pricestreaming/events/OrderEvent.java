package io.hydrax.pricestreaming.events;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.proto.metwo.match.PsOrder;
import io.quarkus.vertx.ConsumeEvent;
import io.smallrye.common.annotation.RunOnVirtualThread;
import io.vertx.core.eventbus.Message;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationScoped
public class OrderEvent {

  final OrderService orderService;
  final ClientManager clientManager;

  @ConsumeEvent(Constant.ORDER)
  @RunOnVirtualThread
  @SuppressWarnings("unused")
  public void onOrder(Message<PsOrder> order) {
    // TODO: need to refactor
    log.trace("OrderEvent: {}", order.body());
    switch (order.body().getRequestType()) {
      case REQUEST_TYPE_NEW_ORDER:
        log.trace("New order");
        orderService.placeOrder(
            Order.builder().headers(order.headers()).psOrder(order.body()).build());
        break;
      case REQUEST_TYPE_EDIT_ORDER:
        log.trace("Amend order");
        break;
      case REQUEST_TYPE_CANCEL_ORDER:
        log.trace("Cancel order");
        orderService.cancelOrder(order.body());
        break;
      default:
        log.debug("Unknown order type: {}", order.body());
    }
  }
}
