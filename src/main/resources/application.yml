server:
  name: smart-order-routing
quarkus:
  hibernate-reactive:
    log:
      sql: true
  datasource:
    reactive:
      url: vertx-reactive:postgresql://localhost:15432/genx_charlie
      max-size: 50
      idle-timeout: 60s
    db-kind: postgresql
    username: postgres
    password: postgres
    metrics:
      enabled: true
  log:
    console:
      enable: true
    category:
      "io.hydrax.pricestreaming":
        level: TRACE
        min-level: TRACE
      "io.hydrax.aeron":
        level: TRACE
        min-level: TRACE
      "io.aeron":
        min-level: TRACE
        level: TRACE
      "org.hibernate.SQL":
        level: DEBUG
    sentry:
      in-app-packages: "*"
      dsn: https://<EMAIL>/****************
      level: ERROR
      minimum-event-level: ERROR
      environment: interim-development-charlie
      release: develop
      traces-sample-rate: 1
      ignored-exceptions-for-type:
        - io.hydrax.pricestreaming.exception.GenericException
        - io.hydrax.pricestreaming.exception.RejectionException
        - io.hydrax.aeron.ConnectionException
        - io.hydrax.aeron.InsufficientException
  http:
    port: 9091
  index-dependency:
    aeron:
      group-id: io.hydrax.aeron

"%dev":
  # Rand Seed:
  enable-rand-seed: false
  rand-seed: 0

  snapshot-interval: 5m
  aeron:
    directory: /Volumes/RAMDisk/sor/aeron-media
    archive:
      archive-directory: /Volumes/RAMDisk/sor/aeron-archive
    clients:
      sor-out:
        target-name: sor-out
        channel: aeron:udp?control=localhost:51022|control-mode=dynamic|ssc=true
        stream-id: 40
        mode: archive
        request-channel: aeron:udp?endpoint=localhost:57010
        request-stream-id: 10
        response-channel: aeron:udp?endpoint=localhost:57021
        response-stream-id: 21
      order-manager:
        target-name: order-manager
        egress-channel: aeron:udp?endpoint=0.0.0.0:50111
        ingress-endpoints: 0=localhost:20110
        ingress-channel: aeron:udp
        ingress-stream-id: 101
        idle-strategy: yield
      venue-sfox:
        target-name: venue-sfox
        egress-channel: aeron:udp?endpoint=0.0.0.0:50111
        ingress-endpoints: 0=localhost:50110
        ingress-channel: aeron:udp
        ingress-stream-id: 204
        idle-strategy: yield
      market-data-gateway:
        target-name: market-data-gateway
        egress-channel: aeron:udp?endpoint=0.0.0.0:50115
        ingress-endpoints: 0=localhost:50310
        ingress-channel: aeron:udp
        ingress-stream-id: 101
        idle-strategy: yield
    cluster:
      directory: /Volumes/RAMDisk/sor/aeron-cluster
      id: 0
      members: 0,localhost:50210
    service:
      idle-strategy: yield
  price:
    period: 3000
