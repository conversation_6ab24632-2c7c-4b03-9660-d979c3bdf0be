syntax = "proto3";

package match_api;

// import "types/types.proto";
import "routines.proto";

option go_package = "me_two/match_api";

option java_package = "io.hydrax.proto.metwo.match";
option java_multiple_files = true;

message TestReq {}

message TestResp {}

// 18 decimals fixed precision (38 totals digits)
message UDec128 {
  uint64 high = 1;
  uint64 low = 2;
}

message PlaceOrderReq {
  string cl_ord_id = 1;
  string symbol = 2;
  Side side = 3;
  OrdType ord_type = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  TimeInForce time_in_force = 23;
  uint64 expire_time = 24;
}

message PsPlaceOrderReq {
  string cl_ord_id = 1;
  string symbol = 2;
  Side side = 3;
  PsOrderType ord_type = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  TimeInForce time_in_force = 23;
  uint64 expire_time = 24;
  UDec128 premium = 25;
  string venue_symbol = 26;
  UDec128 cash_order_qty = 27;
  string fix_request_id = 28;
  string fix_client_order_id = 29;
  string fix_sender_comp_id = 30;
}

message PsCancelOrderReq {
  string orig_cl_ord_id = 1;
  string cl_ord_id = 2;
  string symbol = 3;
  Side side = 4;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  string order_id = 23;
  UDec128 price = 24;
  UDec128 quantity = 25;
}

message CancelOrderReq {
  string orig_cl_ord_id = 1;
  string cl_ord_id = 2;
  string symbol = 3;
  Side side = 4;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
}

message EditOrderReq {
  string orig_cl_ord_id = 1;
  string cl_ord_id = 2;
  string symbol = 3;
  Side side = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string service_account_id = 7;
  string holding_account_id = 8;
  OrdType ord_type = 9;
}

message TransferReq {
  string transfer_id = 1;
  string source = 2; // to indicate sent by BE UI / API
  uint64 db_id = 3;
  repeated TransferList payload = 4;
}

message TransferList {
  string from_balance_account_id = 1;
  string to_balance_account_id = 2;
  UDec128 amount = 3;
  string from_service_account_id = 4;
  string to_service_account_id = 5;
  string contract_id = 6;
  UDec128 price = 7;
  string from_account_id = 8;
  string to_account_id = 9;
  UDec128 hx_earmark_req_amt = 10;
  bool pk_enabled = 11;
}

message SettleTransferReq {
  string transfer_id = 1;
  string source = 2; // to indicate sent by BE UI / API
  uint64 db_id = 3;
  repeated TransferList payload = 4;
  TransferOutcome transfer_outcome = 5;
}

message TransferResp {
  string transfer_id = 1;
  TransferStatus status = 2;
  string error_code = 3;
  string remark = 4;
  uint64 db_id = 5;
}

message PlaceOrderResp {
  string order_id = 1;
}

message CancelOrderResp {
  string order_id = 1;
}

message EditOrderResp {
  string order_id = 1;
}

message Request {
  oneof request {
    PlaceOrderReq place_order = 1;
    CancelOrderReq cancel_order = 2;
    EditOrderReq edit_order = 3;
    DepositReq deposit = 4;
    WithdrawReq withdraw = 5;
    ClearBalanceReq clear_balance = 6;
    ClearOrderbookReq clear_orderbook = 7;
    DisconnectReq disconnect = 8;
    Order order = 9;
    ConfigReq config = 10;
    GetTickersReq get_tickers = 11;
    GetOrderbookReq get_orderbook = 12;
    GetPriceLevelsReq get_price_levels = 13;
    ResponseList match_responses = 14;
    WithdrawEarmarkReq withdraw_earmark = 15;
    TradesConfirmReq trades_confirm = 16;
    PsCancelOrderReq ps_cancel_order = 40;
    PsPlaceOrderReq ps_place_order = 41;
    PsOrder ps_order = 42;
    TransferReq transfer = 43;
    SettleTransferReq settle_transfer = 44;
    ApiEarmark api_earmark = 45;
    AccruedInterestReq accrued_interest = 46;
    OVLeaderReject ov_leader_reject = 47;
    OVLeaderRejectPS ov_leader_reject_ps = 48;
    SORReject sor_reject = 49;
  }
  string source = 20;
  uint64 request_id = 21;
  string sender_comp_id = 22;
  string trace_id = 23;
  string user_id = 24;
  uint64 request_sent_time = 25;
  uint64 request_received_time = 26;
  string from_service = 30; // deprecated (same as source)
  uint64 out_sequence = 31; // deprecated (same as request_id)
  string gateway_name = 32;
  string order_validator_name = 33;
  bool is_message_in_chunk = 34; // flag that use by OV to note message is in chunk
}

enum RequestType {
  REQUEST_TYPE_NEW_ORDER = 0;
  REQUEST_TYPE_CANCEL_ORDER = 1;
  REQUEST_TYPE_EDIT_ORDER = 2;
  REQUEST_TYPE_DISCONNECT = 3;
}

message Order {
  string cl_ord_id = 1;
  string symbol = 2;
  Side side = 3;
  OrdType ord_type = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string orig_cl_ord_id = 7;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  TimeInForce time_in_force = 23;
  string sender_comp_id = 24;
  uint64 request_id = 25;
  string source = 26;
  string user_id = 27;
  string order_id = 30;
  string quote_balance_account_id = 31;
  string base_balance_account_id = 32;
  UDec128 fee_rate = 33;
  UDec128 tax_rate = 34;
  uint64 tax_id = 35;
  string trace_id = 40;
  uint64 request_sent_time = 41;
  RequestType request_type = 42;
  uint64 request_received_time = 43;
  uint64 expire_time = 44;
  FeeResponse hx_fees_resp = 45;
  UDec128 earmark_amt = 46;
  UDec128 earmark_fee_amt = 47;
  UDec128 earmark_tax_amt = 48;
  CancelReason cancel_reason = 49; //use during cancellation @see
  UDec128 earmark_interest_amt = 50;
  UDec128 contract_accr_int_amt = 51; //Accrued Interest Amount of base contract when new order placed
  bool is_trade_on_clean_price = 52;
}

message PsOrder {
  string cl_ord_id = 1;
  string symbol = 2;
  Side side = 3;
  PsOrderType ord_type = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string orig_cl_ord_id = 7;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  TimeInForce time_in_force = 23;
  string sender_comp_id = 24;
  uint64 request_id = 25;
  string source = 26;
  string user_id = 27;
  string order_id = 30;
  string quote_balance_account_id = 31;
  string base_balance_account_id = 32;
  UDec128 fee_rate = 33;
  UDec128 tax_rate = 34;
  uint64 tax_id = 35;
  string trace_id = 40;
  uint64 request_sent_time = 41;
  RequestType request_type = 42;
  uint64 request_received_time = 43;
  uint64 expire_time = 44;
  FeeResponse hx_fees_resp = 45;
  UDec128 earmark_amt = 46;
  UDec128 earmark_fee_amt = 47;
  UDec128 earmark_tax_amt = 48;
  CancelReason cancel_reason = 49; //use during cancellation @see
  UDec128 premium = 50;
  string venue_account_name = 51;                    // Hx fee contract code
  string fix_request_id = 52;
  string original_fix_request_id = 53;
  string market_model = 54;
  string fix_client_order_id = 55;
  string fix_sender_comp_id = 56;
}

message SnapshotOrder {
  string cl_ord_id = 1;
  string symbol = 2;
  Side side = 3;
  OrdType ord_type = 4;
  UDec128 qty = 5;
  UDec128 price = 6;
  string orig_cl_ord_id = 7;
  string service_account_id = 20;
  string holding_account_id = 21;
  string market_code = 22;
  TimeInForce time_in_force = 23;
  string sender_comp_id = 24;
  uint64 request_id = 25;
  string source = 26;
  string user_id = 27;
  string order_id = 30;
  string quote_balance_account_id = 31;
  string base_balance_account_id = 32;
  UDec128 fee_rate = 33;
  UDec128 tax_rate = 34;
  uint64 tax_id = 35;
  string trace_id = 40;
  uint64 request_sent_time = 41;
  RequestType request_type = 42;
  uint64 request_received_time = 43;
  uint64 expire_time = 44;
  FeeResponse hx_fees_resp = 45;
  UDec128 earmark_amt = 46;
  UDec128 earmark_fee_amt = 47;
  UDec128 earmark_tax_amt = 48;
  CancelReason cancel_reason = 49; //use during cancellation @see
  UDec128 open_qty = 50;
  UDec128 cum_trade_qty = 51;
  UDec128 cum_trade_amt = 52;
  UDec128 cum_fee_amt = 53;
  UDec128 cum_tax_amt = 54;
  // repeated FeeStructure fee_structures = 55;
  // repeated TaxStructure tax_structures = 56;
  bool prevent_self_trade = 55;
  UDec128 maker_fee_rate = 56;
  UDec128 taker_fee_rate = 57;
  uint64 create_time = 58;
  UDec128 min_qty = 59;
  int64 timer_id = 60;
  UDec128 earmark_interest_amt = 61;
  bool is_trade_on_clean_price = 62;
}

message RequestList {
  repeated Request requests = 1;
}

enum Side {
  SIDE_BUY = 0;
  SIDE_SELL = 1;
}

enum OrdType {
  ORD_TYPE_MARKET = 0;
  ORD_TYPE_LIMIT = 1;
  ORD_TYPE_STOP = 2;
}

enum ExecType {
  EXEC_TYPE_NEW = 0;
  EXEC_TYPE_TRADE = 1;
  EXEC_TYPE_CANCELED = 2;
  EXEC_TYPE_REPLACED = 3;
  EXEC_TYPE_EXPIRED = 4;
  EXEC_TYPE_REJECTED = 5;
}

enum TimeInForce {
  TIME_IN_FORCE_DAY = 0;
  TIME_IN_FORCE_GOOD_TILL_CANCEL = 1;
  TIME_IN_FORCE_GOOD_TILL_DATE = 2;
  TIME_IN_FORCE_IMMEDIATE_OR_CANCEL = 3;
  TIME_IN_FORCE_FILL_OR_KILL = 4;
}

enum OrdStatus {
  ORD_STATUS_NEW = 0;
  ORD_STATUS_PARTIALLY_FILLED = 1;
  ORD_STATUS_FILLED = 2;
  ORD_STATUS_CANCELED = 3;
  ORD_STATUS_REPLACED = 4;
  ORD_STATUS_EXPIRED = 5;
  ORD_STATUS_REJECTED = 6;
  ORD_STATUS_DONE_FOR_DAY = 7;
}

enum CancelReason {
  EMPTY_REASON = 0;
  // triggered by ME
  CANCEL_DUE_TO_REMAINING_QTY_TOO_LOW = 1;
  CANCEL_DUE_TO_MARKET_CONTROL = 2;
  CANCEL_DUE_TO_EXPIRED_ORDER = 3;
  CANCEL_DUE_TO_UNFILLED_REMAINING_MARKET_ORDER = 4;
  CANCEL_DUE_TO_UNFILLED_LIMIT_IOC = 5;
  CANCEL_DUE_TO_SELF_MATCH_PREVENTION = 6;
  // triggered by fix GW
  CANCEL_DUE_TO_CANCEL_ON_DISCONNECT = 7;
  CANCEL_BY_USER = 8;
  // triggered by BE
  CANCEL_BY_EXCHANGE_OPERATOR = 9;
  CANCEL_DUE_TO_INSUFFICIENT_FUND_RESYNC_BALANCE = 10;
}

message ExecReport {
  string cl_ord_id = 1;
  OrdStatus ord_status = 2;
  UDec128 open_qty = 3;
  Side side = 4;
  UDec128 fill_qty = 5;
  UDec128 last_exec_price = 6;
  UDec128 qty = 7;
  UDec128 fill_avg_price = 8;
  uint64 fix_seq = 10;
  string symbol = 11;
  string order_id = 12;
  uint64 create_time = 13;
  OrdType ord_type = 14;
  UDec128 price = 15;
  string holding_account_id = 16;
  string service_account_id = 17;
  string sender_comp_id = 18;
  UDec128 last_fill_qty = 19;
  UDec128 last_fill_price = 20;
  uint64 transact_time = 21;
  string exec_id = 22;
  string last_trade_id = 23;
  repeated ExecReportFee fees = 24;
  string text = 25;
  ExecType exec_type = 26;
  string trace_id = 27;
  UDec128 earmark_amt = 28;
  UDec128 earmark_fee_amt = 29;
  UDec128 earmark_tax_amt = 30;
  UDec128 fill_fee_amt = 31;
  UDec128 fill_tax_amt = 32;
  UDec128 cum_fill_amt = 33;
  UDec128 cum_fill_fee_amt = 34;
  UDec128 cum_fill_tax_amt = 35;
  bool maker_taker = 36;
  TimeInForce time_in_force = 37;
  string user_id = 38;
  uint64 tax_id = 39;
  string quote_balance_account_id = 40;
  string base_balance_account_id = 41;
  string market_code = 42;
  int64 stream_position = 50;
  map<string, UDec128> order_fees_details = 51;
  map<string, UDec128> trade_fees_details = 52;
  CancelReason cancel_reason = 53;
  uint64 expiry_time = 54;
  int64 backup_stream_position = 55;
  uint64 stream_sequence = 56;
  string from_service = 57;
  bool enable_ext_settlement_confirm = 58;
  bool trade_fee_charged = 59;
  string fees_charge_err_msg = 60;
  UDec128 fill_fee_from_earmark_amt = 61; // skip sell fee feature, case where by Sell fill fee > order fee earmark and < traded amount, and Skip sell is turned on halfway. fee is took from earmark + traded quote amount
  UDec128 fill_fee_from_trade_amt = 62;
  UDec128 fill_tax_from_earmark_amt = 63;
  UDec128 fill_tax_from_trade_amt = 64;
  uint64 response_sent_time = 65;
  uint64 received_time = 66;
  UDec128 last_paid_interest = 67;
  UDec128 paid_interest = 68;
  bool is_trade_on_clean_price = 69;
}

message ExecReportSmall {}

message ExecReportFee {
  UDec128 amount = 1;
  string currency = 2;
  string fee_type = 3;
  string fee_basis = 4;
}

// Price streaming parent order exec report
message PsParentOrderExecReport {
  string asset_holding_account_id = 1;               // Hx asset holding account number
  int64 backup_stream_position = 2;                  // Aeron backup stream position
  UDec128 cumulative_fill_amount = 3;                // Hx cumulative parent order fill amount
  UDec128 cumulative_fill_fee_amount = 4;            // Hx cumulative parent order fee amount
  UDec128 cumulative_fill_quantity = 5;              // Hx cumulative parent order quantity
  UDec128 cumulative_fill_tax_amount = 6;            // Hx cumulative parent order tax amount
  string error = 7;                                  // Hx error
  string exec_id = 8;                                // Execution unique identifier
  UDec128 estimated_fees = 9;                        // Hx original estimated fees
  UDec128 estimated_tax = 10;                        // Hx original estimated tax
  UDec128 exec_fee_amount = 11;                      // Trade fees amount
  UDec128 exec_price = 12;                           // Trade price (venue price and added the calculation of premium)
  UDec128 exec_quantity = 13;                        // Trade quantity
  UDec128 exec_tax_amount = 14;                      // Hx trade tax amount
  uint64 expiry_time = 15;                           // Hx parent order expiry time
  string fee_contract_id = 16;                       // Hx fee contract code
  string fix_request_id = 17;                        // Fix request unique identifier
  string from_service = 18;                          // Exec report from service
  UDec128 include_premium_price = 19;                // Hx trade price (include premium)
  UDec128 last_fill_price = 20;                      // Last trade price (venue price excluding premium)
  UDec128 last_fill_quantity = 21;                   // Last trade quantity
  string last_trade_id = 22;                         // Last trade number
  UDec128 open_quantity = 23;                        // Hx parent order open quantity
  string order_id = 24;                              // Hx parent order number
  PsParentOrderStatus order_status = 25;             // Hx parent order status
  UDec128 premium = 26;                              // Hx parent order premium
  UDec128 price = 27;                                // Hx parent order price (include premium)
  uint64 processed_time = 28;                        // Exec processed time
  PsExecType ps_exec_type = 29;                      // Execution type
  PsOrderType ps_order_type = 30;                    // Order type
  PsSenderService ps_sender_service = 31;            // Sender service information
  UDec128 quantity = 32;                             // Hx parent order quantity
  uint64 request_received_time = 33;                 // Ps place order received time
  string service_account_id = 34;                    // Service account uuid
  Side side = 35;                                    // Hx Parent order side (buy/sell)
  int64 stream_position = 36;                        // Aeron stream position
  uint64 stream_sequence = 37;                       // Aeron stream sequence
  string symbol = 38;                                // Hx ticker code
  uint64 tax_id = 39;                                // Hx tax database identifier
  TimeInForce time_in_force = 40;                    // Time in force
  string trace_id = 41;                              // Trace identifier for distributed tracing
  string user_id = 42;                               // User login identifier
  string venue_account = 43;                         // Trade order venue account
  string venue_fees = 44;                            // Trade fees detail and is an object
  string external_order_id = 45;                     // External order identifier
  string market_code = 46;                           // Market code
  UDec128 earmark_amt = 47;                          // Hx earmark amount
  string base_balance_account_id = 48;               // Base balance account info operator_code:asset_holding_account_number:asset_master_list_code(base contract asset master_list code of the ticker)
  string quote_balance_account_id = 49;              // Quote balance account info operator_code:asset_holding_account_number:asset_master_list_code(quote contract asset master_list code of the ticker)
  UDec128 exec_include_premium_average_price = 50;   // Trade Volume Weighted Average Price (include premium)
  string fix_client_order_id = 51;                   // Fix client order id
  string fix_sender_comp_id = 52;                    // Fix sender company id
  FeeResponse fee_response = 53;                     // Fee response
  UDec128 fee_rate = 54;                             // Hx fee rate
  UDec128 tax_rate = 55;                             // Hx tax rate
  UDec128 remaining_earmark_fee = 56;                // Hx remaining fee amount
  UDec128 remaining_earmark_tax = 57;                // Hx remaining tax amount
  UDec128 trade_fee_amount = 58;                     // Hx trade fee amount
  UDec128 trade_tax_amount = 59;                     // Hx trade tax amount
}

// Price streaming child order exec report and trade exec report and receipts(broker order receipt and broker trade receipt and dealer committed order)
message PsChildOrderExecReport {
  string asset_holding_account_id = 1;               // Hx asset holding account number
  int64 backup_stream_position = 2;                  // Aeron backup stream position
  UDec128 cumulative_fill_amount = 3;                // Hx cumulative child order fill amount
  string conv_factor = 5;                            // Conversion factor
  string error = 6;                                  // Hx error
  UDec128 estimated_fees = 7;                        // Hx original estimated fees
  UDec128 estimated_tax = 8;                         // Hx original estimated tax
  string exec_id = 9;                                // Execution unique identifier
  UDec128 exec_average_price = 10;                   // Trade Volume Weighted Average Price
  string exec_fees_details = 11;                     // Trade fees detail and is an object
  UDec128 exec_price = 12;                           // Trade price (excluding premium)
  UDec128 exec_quantity = 13;                        // Trade quantity
  string fix_request_id = 14;                        // Fix request unique identifier
  string from_service = 15;                          // Exec report from service
  UDec128 include_premium_price = 16;                // Hx trade price (include premium)
  UDec128 last_fill_price = 17;                      // Last trade price (excluding premium)
  UDec128 last_fill_quantity = 18;                   // Last trade quantity
  UDec128 leaves_quantity = 19;                      // Amount remaining of the order
  string order_id = 20;                              // Hx child order number, equal to the external venue's ClOrdID
  PsChildOrderStatus order_status = 21;              // Hx child order status
  string original_fix_request_id = 22;               // Original Fix request unique identifier
  string parent_order_id = 23;                       // Hx parent order number
  UDec128 price = 24;                                // Hx child order price (excluding premium)
  uint64 processed_time = 25;                        // Exec processed time
  PsExecType ps_exec_type = 26;                      // Execution type
  PsOrderType ps_order_type = 27;                    // Order type
  string ps_trade_id = 28;                           // Hx trade number
  PsTradeStatus ps_trade_status = 29;                // Hx trade status
  UDec128 quantity = 30;                             // Hx child order quantity
  string reject_reason = 31;                         // Trade order reject reason
  string rejected_request_type = 32;                 // Trade order reject request type
  Side side = 33;                                    // Trade side (buy/sell)
  int64 stream_position = 34;                        // Aeron stream position
  uint64 stream_sequence = 35;                       // Aeron stream sequence
  string symbol = 36;                                // Hx ticker code
  uint64 tax_id = 37;                                // Hx tax database identifier
  uint64 transact_time = 38;                         // Exec trade transact time
  TimeInForce time_in_force = 39;                    // Time in force
  string user_id = 40;                               // User login identifier
  string venue_account = 41;                         // Trade order venue account
  string venue_code = 42;                            // Trade venue market code
  string venue_fees = 43;                            // Trade fees detail and is an object
  string venue_order_id = 44;                        // Venue order identifier
  string venue_symbol = 45;                          // Trade venue symbol
  string market_model = 46;                          // Hx market model Broker/Dealer
  PsSenderService ps_sender_service = 47;            // Sender service information
  string service_account_id = 48;                    // Service account uuid
  UDec128 premium = 49;                              // Hx premium
  UDec128 trade_fee_amount = 50;                     // Hx trade fee amount
  UDec128 trade_tax_amount = 51;                     // Hx trade tax amount
  string member_asset_holding_account_id = 52;       // dealer asset holding account number
}

enum PsParentOrderStatus {
  PS_PARENT_ORDER_STATUS_PENDING = 0;
  PS_PARENT_ORDER_STATUS_PARTIALLY_FILLED = 2;
  PS_PARENT_ORDER_STATUS_CONFIRMED = 1;
  PS_PARENT_ORDER_STATUS_PENDING_REPLACE = 3;
  PS_PARENT_ORDER_STATUS_REPLACED = 4;
  PS_PARENT_ORDER_STATUS_PENDING_CANCEL = 5;
  PS_PARENT_ORDER_STATUS_CANCELED = 6;
  PS_PARENT_ORDER_STATUS_REJECTED = 7;
  PS_PARENT_ORDER_STATUS_ERROR = 8;
  PS_PARENT_ORDER_STATUS_EXPIRED = 9;
}

enum PsChildOrderStatus {
  PS_CHILD_ORDER_STATUS_PENDING = 0;
  PS_CHILD_ORDER_STATUS_PARTIALLY_FILLED = 1;
  PS_CHILD_ORDER_STATUS_CONFIRMED = 2;
  PS_CHILD_ORDER_STATUS_PENDING_REPLACE = 3;
  PS_CHILD_ORDER_STATUS_REPLACED = 4;
  PS_CHILD_ORDER_STATUS_PENDING_CANCEL = 5;
  PS_CHILD_ORDER_STATUS_CANCELED = 6;
  PS_CHILD_ORDER_STATUS_REJECTED = 7;
  PS_CHILD_ORDER_STATUS_ERROR = 8;
  PS_CHILD_ORDER_STATUS_DONE_FOR_DAY = 9;
}

enum PsTradeStatus {
  PS_TRADE_STATUS_PENDING = 0;
  PS_TRADE_STATUS_CONFIRMED = 1;
  PS_TRADE_STATUS_REPLACED = 2;
  PS_TRADE_STATUS_CANCELED = 3;
  PS_TRADE_STATUS_ERROR = 4;
}

enum PsExecType {
  PS_EXEC_TYPE_NEW = 0;
  PS_EXEC_TYPE_TRADE = 1;
  PS_EXEC_TYPE_CANCELED = 2;
  PS_EXEC_TYPE_CANCEL_REJECT = 3;
  PS_EXEC_TYPE_REJECTED = 4;
}

enum PsOrderType {
  PS_ORDER_TYPE_MARKET = 0;
  PS_ORDER_TYPE_LIMIT = 1;
  PS_ORDER_TYPE_STOP = 2;
}

enum PsSenderService {
  PS_SENDER_SERVICE_SOR = 0;
  PS_SENDER_SERVICE_LP = 1;
}

message Trade {
  string trade_id = 1;
  string symbol = 2;
  Side side = 3;
  UDec128 qty = 4;
  UDec128 price = 5;
  string maker_order_id = 6;
  string taker_order_id = 7;
  uint64 time = 8;
  UDec128 maker_fee = 9;
  UDec128 taker_fee = 10;
}

message PriceUpdate {
  string symbol = 1;
  Side side = 2;
  UDec128 price = 3;
  UDec128 qty = 4;
  uint64 time = 5;
  uint32 depth = 6;
  uint64 offset = 7;
  UDec128 last_trade_price = 8;
  uint64 last_trade_time = 9;
  UDec128 last_trade_qty = 10;
  uint64 num_orders = 11;
  string trading_session_id = 12;
  string timezone = 13;
  int64 stream_position = 14;
  uint64 stream_sequence = 15;
  string from_service = 16;
  UDec128 old_qty = 17;
  string request_id = 18;
  int64 backup_stream_position = 19;
  uint64 response_sent_time = 20;
  uint64 received_time = 21;
}


enum BalanceUpdateType {
  BALANCE_UPDATE_TOTAL_AVAIL = 0;
  BALANCE_UPDATE_AVAIL_ONLY = 1;
  BALANCE_UPDATE_TOTAL_ONLY = 2;
  REJECT_BALANCE_UPDATE = 3;
  BALANCE_UPDATE_PENDING_IN = 4;
  BALANCE_UPDATE_PENDING_OUT = 5;
  BALANCE_UPDATE_CONFIRM_PENDING_IN = 6;
  BALANCE_UPDATE_CONFIRM_PENDING_OUT = 7;
  BALANCE_UPDATE_INITIALIZE_BALANCE = 8; // use for balance migration AGX PROD, initial is aim to set OV balance from db if OV in-memory balance is empty and db_wr shall by pass
  BALANCE_UPDATE_REPLACE_BALANCE = 9; // use for balance migration AGX PROD, replace is aim to replace OV balance from db and db_wr shall by pass
}

enum ScenarioType {
  SCENARIO_TYPE_BALANCE = 0;
  SCENARIO_TYPE_FEES = 1;
  SCENARIO_TYPE_TAX = 2;
  SCENARIO_TYPE_INTEREST = 3;
}

enum Scenario {
  SCENARIO_DEPOSIT = 0;
  SCENARIO_WITHDRAWAL = 1;
  SCENARIO_NEW_ORDER_BUY = 2;
  SCENARIO_NEW_ORDER_BUY_FEES = 3;
  SCENARIO_NEW_ORDER_BUY_TAX = 4;
  SCENARIO_NEW_ORDER_SELL = 5;
  SCENARIO_NEW_ORDER_SELL_FEES = 6;
  SCENARIO_NEW_ORDER_SELL_TAX = 7;
  SCENARIO_CANCEL_ORDER_BUY = 8;
  SCENARIO_CANCEL_ORDER_BUY_FEES = 9;
  SCENARIO_CANCEL_ORDER_BUY_TAX = 10;
  SCENARIO_CANCEL_ORDER_SELL = 11;
  SCENARIO_CANCEL_ORDER_SELL_FEES = 12;
  SCENARIO_CANCEL_ORDER_SELL_TAX = 13;
  SCENARIO_CONFIRM_BUY_BASE = 14;
  SCENARIO_CONFIRM_BUY_QUOTE = 15;
  SCENARIO_CONFIRM_SELL_BASE = 16;
  SCENARIO_CONFIRM_SELL_QUOTE = 17;
  SCENARIO_CONFIRM_FEES_TAKER_BUY = 18;
  SCENARIO_CONFIRM_FEES_TAKER_SELL = 19;
  SCENARIO_CONFIRM_FEES_MAKER_BUY = 20;
  SCENARIO_CONFIRM_FEES_MAKER_SELL = 21;
  SCENARIO_CONFIRM_TAX_TAKER_BUY = 22;
  SCENARIO_CONFIRM_TAX_TAKER_SELL = 23;
  SCENARIO_CONFIRM_TAX_MAKER_BUY = 24;
  SCENARIO_CONFIRM_TAX_MAKER_SELL = 25;
  SCENARIO_RETURN_EARMARK_BUY = 26;
  SCENARIO_RETURN_EARMARK_BUY_FEES = 27;
  SCENARIO_RETURN_EARMARK_BUY_TAX = 28;
  SCENARIO_RETURN_EARMARK_SELL = 29;
  SCENARIO_RETURN_EARMARK_SELL_FEES = 30;
  SCENARIO_RETURN_EARMARK_SELL_TAX = 31;
  SCENARIO_REVERSE_EARMARK_BUY = 32;
  SCENARIO_REVERSE_EARMARK_BUY_FEES = 33;
  SCENARIO_REVERSE_EARMARK_BUY_TAX = 34;
  SCENARIO_REVERSE_EARMARK_SELL = 35;
  SCENARIO_REVERSE_EARMARK_SELL_FEES = 36;
  SCENARIO_REVERSE_EARMARK_SELL_TAX = 37;
  SCENARIO_REJECT_ORDER_BUY = 38;
  SCENARIO_REJECT_ORDER_BUY_FEES = 39;
  SCENARIO_REJECT_ORDER_BUY_TAX = 40;
  SCENARIO_REJECT_ORDER_SELL = 41;
  SCENARIO_REJECT_ORDER_SELL_FEES = 42;
  SCENARIO_REJECT_ORDER_SELL_TAX = 43;

  SCENARIO_PENDING_BUY_BASE = 44;
  SCENARIO_PENDING_BUY_QUOTE = 45;
  SCENARIO_PENDING_FEES_TAKER_BUY = 46;
  SCENARIO_PENDING_TAX_TAKER_BUY = 47;
  SCENARIO_PENDING_FEES_MAKER_BUY = 48;
  SCENARIO_PENDING_TAX_MAKER_BUY = 49;
  SCENARIO_PENDING_SELL_QUOTE = 50;
  SCENARIO_PENDING_SELL_BASE = 51;
  SCENARIO_PENDING_FEES_TAKER_SELL = 52;
  SCENARIO_PENDING_TAX_TAKER_SELL = 53;
  SCENARIO_PENDING_FEES_MAKER_SELL = 54;
  SCENARIO_PENDING_TAX_MAKER_SELL = 55;
  SCENARIO_RELEASE_WITHDRAW = 56;

  SCENARIO_REVERSE_PENDING_BUY_BASE = 57;
  SCENARIO_REVERSE_PENDING_BUY_QUOTE = 58;
  SCENARIO_REVERSE_PENDING_FEES_TAKER_BUY = 59;
  SCENARIO_REVERSE_PENDING_TAX_TAKER_BUY = 60;
  SCENARIO_REVERSE_PENDING_FEES_MAKER_BUY = 61;
  SCENARIO_REVERSE_PENDING_TAX_MAKER_BUY = 62;
  SCENARIO_REVERSE_PENDING_SELL_QUOTE = 63;
  SCENARIO_REVERSE_PENDING_SELL_BASE = 64;
  SCENARIO_REVERSE_PENDING_FEES_TAKER_SELL = 65;
  SCENARIO_REVERSE_PENDING_TAX_TAKER_SELL = 66;
  SCENARIO_REVERSE_PENDING_FEES_MAKER_SELL = 67;
  SCENARIO_REVERSE_PENDING_TAX_MAKER_SELL = 68;

  SCENARIO_EARMARK = 69;
  SCENARIO_TRANSFER = 70;
  SCENARIO_TRANSFER_COMPLETED = 71;
  SCENARIO_TRANSFER_REVERSED = 72;
  SCENARIO_TRANSFER_REVERSED_FAILURE = 73;

  SCENARIO_API_EARMARK = 74;
  SCENARIO_API_EARMARK_REVERSE = 75;
  SCENARIO_EARMARK_ADJUSTMENT = 76;
  SCENARIO_REVERSE_EARMARK_ADJUSTMENT = 77;
  SCENARIO_NEW_ORDER_BUY_INTEREST = 78;
  SCENARIO_REVERSE_EARMARK_BUY_INTEREST = 79;
  SCENARIO_PENDING_BUY_QUOTE_INTEREST = 80;
  SCENARIO_PENDING_SELL_QUOTE_INTEREST = 81;
  SCENARIO_REVERSE_PENDING_BUY_QUOTE_INTEREST = 82;
  SCENARIO_REVERSE_PENDING_SELL_QUOTE_INTEREST = 83;
  SCENARIO_CONFIRM_BUY_QUOTE_INTEREST = 84;
  SCENARIO_CONFIRM_SELL_QUOTE_INTEREST = 85;
  SCENARIO_CANCEL_ORDER_BUY_INTEREST = 86;
  SCENARIO_REJECT_ORDER_BUY_INTEREST = 87;
}

message BalanceUpdate {
  string balance_account_id = 1;
  string asset_symbol = 2;
  string ref = 3;
  BalanceUpdateType update_type = 4;
  UDec128 update_amount = 5;
  UDec128 balance_total = 6;
  UDec128 balance_avail = 7;
  uint64 time = 8;
  string market_code = 9;
  string balance_update_id = 10;
  string order_id = 11;
  ScenarioType scenario_type = 12;
  Scenario scenario = 13;
  string deposit_id = 14;
  string withdrawal_id = 15;
  bool neg_update_amount = 16;
  string transfer_id = 17;
  int64 stream_position = 50;
  uint64 stream_sequence = 51;
  string from_service = 52;
  PositionKeepingInfo position_keeping_info = 53;
  string error_code = 54;
  string remark = 55;
  UDec128 total_pending_in = 56;
  UDec128 total_pending_out = 57;
  string trade_id = 58;
  bool no_reverse_earmark = 59;
  UDec128 deposit_withdrawal_cost = 60;
  string maker_taker_type = 61;
  TradeConfirmInfo trade_confirm_info = 62; // used for send notification to FE during trade settlement confirm
  int64 backup_stream_position = 63;
  uint64 response_sent_time = 64;
  uint64 received_time = 65;
  ApiEarmark api_earmark = 66; // earmark from market_ops v2
  WithdrawEarmarkResp withdraw_earmark = 67;
  WithdrawResp withdraw = 68;
  TransferResp transfer = 69;
}

message TradeConfirmInfo {
  string ticker_code = 1;
  string ticker_name = 2;
  string side = 3;
  string ord_status = 4;
  string qty = 5;
  string price = 6;
  string order_number = 7;
  string mkt_id = 8;
}

message AccruedInterestUpdate{
  string market_id = 1;
  string market_code = 2;
  string contract_code = 3;
  UDec128 accrued_interest = 4;
}

message Response {
  oneof response {
    ExecReport exec_report = 1;
    Trade trade = 2; // not used by hx dbwr
    PriceUpdate price_update = 3;
    BalanceUpdate balance_update = 4;
    WithdrawResp withdraw = 5;
    DepositResp deposit = 6;
    GetTickersResp get_tickers = 7;
    GetOrderbookResp get_orderbook = 8;
    GetPriceLevelsResp get_price_levels = 9;
    CancelByOperatorResp cancel_by_operator_resp = 10;
    WithdrawEarmarkResp withdraw_earmark = 11;
    TradeConfirmResp    trade_confirm_resp = 12;
    ClearPrice          clear_price = 13;
    PsParentOrderExecReport    ps_parent_order_exec_report = 14;
    PsChildOrderExecReport    ps_child_order_exec_report = 15;
    TransferResp transfer_resp = 16;
    ApiEarmarkResp api_earmark_resp = 17;
    AccruedInterestResp accrued_interest_resp = 18;
    AccruedInterestUpdate accrued_interest_update = 19;
  }
}

message ResponseList {// 1 kafka message sent out per order
  repeated Response responses = 1;
  string source = 2;
  uint64 request_id = 3;
  string sender_comp_id = 4;
  uint64 request_sent_time = 5;
  uint64 request_received_time = 6;
  uint64 response_sent_time = 7;
  string from_service = 30;
  uint64 out_sequence = 31;
  uint64 match_out_pos = 32;
  uint64 match_out_backup_pos = 33;
  uint64 sor_out_pos = 34;
  uint64 sor_out_backup_pos = 35;
  bool is_message_in_chunk = 36; // flag that use by match to note message is in chunk
}

message FeeLevel {
  UDec128 min_amount = 1;
  UDec128 max_amount = 2;
  UDec128 fixed_fee = 3;
  UDec128 basis_points = 4;
  uint64  level_id = 5;
}

enum FeeType {
  FEE_TIERED = 0;
  FEE_BANDED = 1;
  FEE_TIERED_RATIO = 2;
  FEE_BANDED_RATIO = 3;
}

message FeeStructure {
  FeeType fee_type = 1;
  UDec128 min_fee = 2;
  UDec128 max_fee = 3;
  repeated FeeLevel fee_levels = 4;
  UDec128 ratio_x = 5;
  UDec128 ratio_y = 6;
  string description = 7;
  string currency = 8;
  bool fee_base = 9; // true = derive fee using amount, false derive fee using qty
}

enum DepositStatus {
  DEPOSIT_STATUS_DONE = 0;
  DEPOSIT_STATUS_ERROR = 1;
}

message DepositReq {
  string deposit_id = 1;
  string balance_account_id = 2;
  UDec128 amount = 3;
  bool pk_enabled = 4;
  UDec128 cost = 5;
  string contract_id = 6; // ex.contract.id
  string service_account_id = 7; // saa.uuid
  string account_id = 8; // aha.uuid
}

message DepositResp {
  string deposit_id = 1;
  DepositStatus status = 2;
  string error_code = 3;
  string remark = 4;
}

message WithdrawReq {
  string withdraw_id = 1;
  string balance_account_id = 2;
  UDec128 amount = 3;
  bool pk_enabled = 4;
  string contract_id = 5; // ex.contract.id
  string service_account_id = 6; // saa.uuid
  bool wd_include_to_realize_pnl = 7; // only for withdrawal. flag for including the wd amount to realize PnL
  UDec128 price = 8;
  WithdrawOutcome withdraw_outcome = 9;
  string account_id = 10; // aha.uuid
  uint64 db_id = 11;
}

message WithdrawEarmarkReq {
  string withdraw_id = 1;
  string balance_account_id = 2;
  UDec128 amount = 3;
  bool pk_enabled = 4;
  string contract_id = 5; // ex.contract.id
  string service_account_id = 6; // saa.uuid
  bool wd_include_to_realize_pnl = 7; // only for withdrawal. flag for including the wd amount to realize PnL
  UDec128 price = 8;
  string account_id = 9; // aha.uuid
  uint64 db_id = 10;
}

enum WithdrawStatus {
  WITHDRAW_STATUS_DONE = 0;
  WITHDRAW_STATUS_ERROR = 1;
  WITHDRAW_STATUS_PENDING = 2;
}

enum WithdrawOutcome {
  WITHDRAW_OUTCOME_SUCCESS = 0;
  WITHDRAW_OUTCOME_ERROR = 1;
  WITHDRAW_OUTCOME_REJECTED = 2;
  WITHDRAW_OUTCOME_CANCELLED = 3;
}

enum TransferOutcome {
  TRANSFER_OUTCOME_SUCCESS = 0;
  TRANSFER_OUTCOME_FAILURE = 1;
}

enum TransferStatus {
  TRANSFER_STATUS_DONE = 0;
  TRANSFER_STATUS_ERROR = 1;
  TRANSFER_STATUS_PENDING = 2;
}

message WithdrawResp {
  string withdraw_id = 1;
  WithdrawStatus status = 2;
  string error_code = 3;
  string remark = 4;
  uint64 db_id = 5;
}

message WithdrawEarmarkResp {
  string withdraw_id = 1;
  WithdrawStatus status = 2;
  string error_code = 3;
  string remark = 4;
  uint64 db_id = 5;
}

message UpdateBalancesReq {
  repeated BalanceUpdate updates = 1;
}

message UpdateBalancesResp {
  string error = 1;
}

message CancelByOperatorResp {
  bool success = 1;
  string error = 2;
}

message GetTickersReq {}

message GetTickersResp {
  repeated Ticker tickers = 1;
}

message Ticker {
  string symbol = 1;
  UDec128 bid_price = 2;
  UDec128 bid_qty = 3;
  UDec128 ask_price = 4;
  UDec128 ask_qty = 5;
}

message GetOrderbookReq {
  string symbol = 1;
}

message GetPriceLevelsReq {
  string symbol = 1;
}

message PriceLevel {
  UDec128 price = 1;
  UDec128 qty = 2;
  uint64 offset = 3;
  uint32 depth = 4;
  uint64 time = 5;
  uint64 num_orders = 6;
}

message GetOrderbookResp {
  string symbol = 1;
  repeated PriceLevel bids = 2;
  repeated PriceLevel asks = 3;
  UDec128 last_trade_price = 4;
  uint64 last_trade_time = 5;
  UDec128 last_trade_qty = 6;
  bool only_trades = 7;
}

message GetPriceLevelsResp {
  repeated GetOrderbookResp orderbooks = 1;
}

message ClearOrderbookReq {
  string symbol = 1;
}

enum DisconnectType {
  DISCONNECT_TYPE_FIX_SESSION = 0;
  DISCONNECT_TYPE_FIX_GATEWAY = 1;
  DISCONNECT_TYPE_ORDER_VALIDATOR = 2;
  DISCONNECT_TYPE_MATCH_ENGINE = 3;
}

message DisconnectReq {
  DisconnectType disconnect_type = 1;
  string sender_comp_id = 2;
  string gateway_name = 3;
  string order_validator_name = 4;
  string match_engine_name = 5;
}

message ClearOrderbookResp {}

message ClearBalanceReq {
  string balance_account_id = 1;
}

message ClearBalanceResp {}

message ReloadReq {}

message ReloadResp {}

message ExitReq {}

message ExitResp {}

message OrderbookSnapshot {
  string symbol = 1;
  repeated SnapshotOrder open_orders = 2;
  UDec128 last_trade_price = 3;
  uint64 last_trade_time = 4;
  UDec128 last_trade_qty = 5;
  uint64 next_price_offset = 6;
}

message MatchSnapshot {
  repeated OrderbookSnapshot orderbooks = 1;
  bytes rand_state = 2;
  string service_name = 3;
  uint64 out_sequence = 4;
  map<string, uint64> last_request_ids = 5;
  uint64 ov_in_sequence = 6;
  map<string, MarketSnapshot> market_map = 7;
  map<string, UserSnapshot> user_map = 8;
  map<string, SelfTradeMapSnapshot> self_trade_map = 9; // key is account ID, value is a set of map of market codes.
  int64 aeron_schedule_timer_count = 10; // expired order use the counter as scheduler correlationId id
  string exec_id_format = 11;
  uint64 exec_id_seq = 12;
  string trade_id_format = 13;
  uint64 trade_id_seq = 14;
}

message BalanceAccountSnapshot {
  string balance_account_id = 1;
  UDec128 avail_balance = 2;
  UDec128 total_balance = 3;
  UDec128 earmark_withdraw = 4;
  UDec128 total_pending_in = 6;
  UDec128 total_pending_out = 7;
  map<string, AdjustEarmarkBalance> adjust_earmark_balances = 8;
}

message AdjustEarmarkBalance {
  string order_id = 1;
  UDec128 amount = 2;
}

message OvSnapshot {
  repeated BalanceAccountSnapshot balance_accounts = 1;
  bytes rand_state = 2;
  string service_name = 3;
  uint64 out_sequence = 4;
  map<string, uint64> last_request_ids = 5;
  uint64 me_in_sequence = 6;
  uint64 match_out_last_pos = 7;
  uint64 match_out_backup_last_pos = 8;
  map<string, MarketSnapshot> market_map = 9;
  map<string, TickerSnapshot> ticker_map = 10;
  map<string, HoldingAccountSnapshot> holding_accounts_map = 11;
  map<string, ServiceAccountSnapshot> service_access_accounts_map = 12;
  map<string, MemberTypeSnapshot> member_type_map = 13;
  map<string, FeeAssignmentSnapshot> place_order_fee_assignment_map = 14;
  map<string, FeeAssignmentSnapshot> fill_fee_assignment_map = 15;
  map<string, ActivityFeeRuleContainerSnapshot> activity_fee_rule_containers_map = 16;
  map<string, MemberTypeTaxSnapshotList> member_type_tax_map = 17;
  map<string, OVOrderBookSnapshot> order_book_map = 18;
  map<string, uint64> match_out_sequence = 19;
  string order_number_format = 20;
  uint64 order_number_seq = 21;
  string exec_id_format = 22;
  uint64 exec_id_seq = 23;
  string balance_id_format = 24;
  uint64 balance_id_seq = 25;
  BondSnapshot bond_snapshot = 26;
  map<int64, BondContract> accrued_interest_timer_events = 27; // Deprecated: moved to bond_snapshot
  int64 interest_timer_count = 28; // Deprecated: moved to bond_snapshot
  map<string, uint64> sor_out_sequence = 29;
  repeated PsOrderRemainEarmarkSnapshot ps_order_remain_earmark = 30;
  uint64 sor_out_last_pos = 31;
  string ps_order_number_format = 32;
  uint64 ps_order_number_seq = 33;
  map<string, VenueAccountUpdateRequest> venue_accounts = 34;
  repeated PsEarmarkInfo ps_earmark_info = 35;
}

message ORESnapshot {
  repeated OREOrderBookSnapshot orderbooks = 1;
  repeated VenueMarketUpdateRequest venue_market_updates = 2;
  repeated VenueAccountUpdateRequest venue_account_updates = 3;
  repeated OrderRoutingStrategyUpdateRequest order_routing_strategy_updates = 4;
  uint64 sequence = 5;
  uint64 id_sequence = 6;
  bytes most_order_ids = 7;
  bytes least_order_ids = 8;
  repeated PsOrder open_orders = 9;
  repeated ChildOrderParentOrderSnapshot child_order_parent_order_snapshots = 10;
  repeated TickerUpdateReq ticker_updates = 11;
  repeated MarketUpdateReq market_updates = 12;
  repeated TradeVenueSequence trade_venue_sequences = 13;
  uint64 price_update_sequence = 14;
  repeated ParentOrderRemainingEarmark parent_order_remaining_earmark = 15;
}

message ParentOrderRemainingEarmark {
  int64 parent_order_id = 1;
  UDec128 remaining_earmark_fee = 2;
  UDec128 remaining_earmark_tax = 3;
}

message TradeVenueSequence {
  string venue_code = 1;
  uint64 sequence = 2;
}

message PsOrderRemainEarmarkSnapshot {
  string parent_order_id = 1;
  UDec128 remain_earmark = 2;
}

message PsEarmarkInfo {
  string order_id = 1;
  UDec128 remaining_earmark = 2;
  UDec128 remaining_earmark_fee = 3;
  UDec128 remaining_earmark_tax = 4;
  string operator_earmark_balance_account_id = 5;
}

message ChildOrderParentOrderSnapshot {
  string child_order_id = 1;
  uint64 parent_order_id = 2;
  string child_order_sender_service = 3;
}

message BondSnapshot {
  map<string, AccruedInterest> accrued_interest_map = 1;
  map<string, BondOrders> earmark_recalc_orders = 2;
  int64 interest_timer_count = 3;
  map<int64, BondContract> accrued_interest_timer_events = 4;
}

message AccruedInterest {
  string market_code = 1;
  string base_asset_code = 2;
  UDec128 accrued_interest = 3;
}

message BondContract {
  int64 id = 1;
  string asset_id = 2; // ex_contract.code
  int64 asset_amount_decimals = 3; // ex_contract.digits
  string balance_account_code = 4; // ex_asset_master_list.code
  string instrument_type = 5;
  bool is_trade_on_clean_price = 6;
  string bond_daycount = 7;
  UDec128 coupon_rate = 8;
  string coupon_rate_type = 9;
  string dated_date = 10;
  UDec128 face_value = 11;
  string interest_calculation_time = 12;
  string issue_date = 13;
  string maturity_date = 14;
  string timezone = 15;
  int64 denomination_currency_id = 16;
  string denomination_currency_code = 17;
  int64 timer_id = 18;
  repeated CouponDate coupon_dates = 19;
  string market_code = 20;
  int64 market_id = 21;
}


message BondOrders {
  repeated string ids = 1;
}


// HX Config
// ENUM Action For Config Update
enum ConfigAction {
  ADD = 0;
  UPDATE = 1;
  DELETE = 2;
  CLEAR = 3;
}

message AhaUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string uuid = 3;
  string number = 4;
  bool can_deposit = 5;
  bool can_transfer = 6;
  bool can_withdraw = 7;
  string status = 8;
  string account_type = 9;
  repeated int64 market_ids = 10;
  repeated string market_codes = 11;
  int64 service_registry_id = 12;
  string service_name = 13;
  repeated string self_trade_markets = 14;
  repeated string market_saa_link = 15;  // format: marketCode|saaUuid
  string account_type_code = 16;
  string member_type_code = 17;
}

message ContractUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string name = 3;
  string code = 4;
  int64 market_id = 5;
  int64 asset_master_list_id = 6;
  int64 decimal_places = 7;
  string market_code = 8;
  string asset_master_list_code = 9;
  string instrument_type = 10;
  string bond_daycount = 11;
  UDec128 coupon_rate = 12;
  string coupon_rate_type = 13;
  string dated_date = 14;
  UDec128 face_value = 15;
  string interest_calculation_time = 16;
  bool is_trade_on_clean_price = 17;
  string issue_date = 18;
  string maturity_date = 19;
  string timezone = 20;
  int64 denomination_currency_id = 21;
  string denomination_currency_code = 22;
  repeated CouponDate coupon_dates = 23;
}
message CouponDate {
  int64 id = 1;
  string record_date = 2;
  string payment_date = 3;
}

message MarketUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string name = 3;
  string code = 4;
  string timezone = 5;
  bool allow_self_trade = 6;
  bool enable_fees = 7;
  bool enable_risk_control = 8;
  repeated string order_types = 9;
  repeated string market_tifs = 10;
  repeated string limit_tifs = 11;
  repeated string stop_tifs = 12;
  int64 operator_id = 13;
  string operator_code = 14;
  bool skip_sell_fees_earmark = 15;
  bool enable_custody = 16;
  bool position_keeping = 17;
  bool enable_market_surveillance = 18;
  string order_expiry_time = 19;
  bool enable_ext_settlement_confirm = 20;
  string market_model = 21;
}

message OperatorUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string name = 3;
  string code = 4;
  bool enable_fees = 5;
  bool enable_risk_control = 6;
  bool skip_sell_fees_earmark = 7;
  bool enable_custody = 8;
  bool position_keeping = 9;
  bool enable_market_surveillance = 10;
}

message SaaUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string uuid = 3;
  string name = 4;
  string status = 5;
  bool can_buy = 6;
  bool can_sell = 7;
  int64 member_id = 8;
  string member_name = 9;
  string member_code = 10;
  int64 market_id = 11;
  string market_name = 12;
  string market_code = 13;
  repeated int64 aha_ids = 14;
  repeated string aha_uuids = 15;
  repeated string market_aha_link = 16;  // format: marketCode|ahaName
  string service_account_type_code = 17;
}

message TickerUpdateReq {
  ConfigAction action = 1;
  int64 id = 2;
  string name = 3;
  string code = 4;
  int64 ticker_qty_dp = 5;
  int64 ticker_price_dp = 6;
  UDec128 min_order_qty = 7;
  UDec128 max_order_qty = 8;
  UDec128 order_qty_multiple = 9;
  UDec128 order_price_multiple = 10;
  bool can_trade = 11;
  UDec128 buffer_pct = 12;
  int64 market_id = 13;
  string market_code = 14;
  int64 service_registry_id = 15;
  string base_contract_code = 16;
  string base_aml_code = 17;
  string quote_contract_code = 18;
  string quote_aml_code = 19;
  int64 base_contract_dp = 20;
  int64 quote_contract_dp = 21;
  string liquidity_ladder = 22;
}

message MarketRoutineUpdateReq {
  ConfigAction action = 1;
  RoutineConfig routine_config = 2;
}

// TimeControl
message TimeControl {
  string date = 1;
  string market_code = 2;
  string timezone = 3;
  string price_discovery_model = 4;
  string ticker_code = 5;
  string routine_type = 6;
  string period_name = 7;
  string start_time = 8;
  string end_time = 9;
  bool can_create = 10;
  bool can_cancel = 11;
  bool can_amend = 12;
  bool can_match = 13;
  bool auto_recover = 14;
  string routine_state_code = 15;
  string routine_state_name = 16;
  bool purge_open_order = 17;
}

message TimeControlUpdateReq {
  ConfigAction action = 1;
  repeated TimeControl time_controls = 4;
}

message UserUpdateReq {
  ConfigAction action = 1;
  string login = 2;
  string om_sender_comp_id = 3;
  bool cancel_on_disconnect = 4;
}

message ConfigReq {
  oneof request {
    ActivityFeeRuleUpdateReq activity_fee_rule_update = 1;  // Fee Engine Fee Rule Update From Middleware
    TaxUpdateReq tax_update = 2;                            // Tax Config Update From Middleware
    FeeAssignmentUpdateReq fees_assigment_update = 3;       // Fee Assigment Config Update From Middleware
    AhaUpdateReq aha_update = 4;
    ContractUpdateReq contract_update = 5;
    MarketUpdateReq market_update = 6;
    OperatorUpdateReq operator_update = 7;  // Not used
    SaaUpdateReq saa_update = 8;
    TickerUpdateReq ticker_update = 9;
    MarketRoutineUpdateReq market_routine_update = 10;
    TimeControlUpdateReq time_control_update = 11;
    UserUpdateReq user_update = 12;
    OrderRoutingStrategyUpdateRequest order_routing_strategy_update = 21;
    VenueMarketUpdateRequest venue_market_update = 22;
    VenueAccountUpdateRequest venue_account_update = 23;
    MigrateBalanceReq migrate_balance_req = 13;
  }
}

// 2. Activity Fee Rule Config
// ============================================================================================
message ActivityFeeRuleUpdateReq {
  ConfigAction action = 1;
  ActivityFeeRule activity_fee_rule = 2;
}

// NOTE 1 code|attr can have multiple rule_id
message ActivityFeeRule {
  uint64 rule_id = 1;
  string activity_code = 2;
  string activity_attribute = 3;
  string description = 4;
  FeeType fee_rule_type = 5;
  string currency = 6;
  UDec128 min_fee = 7;
  UDec128 max_fee = 8;
  bool derive_on_amount = 9;
  // tiered/banded
  repeated FeeLevel fee_levels = 10;
  // ratio
  UDec128 ratio_x = 11;
  UDec128 ratio_y = 12;
}


// 3. Tax Config, Tax Config is configured using memberTypeId from BE
// ============================================================================================
message TaxUpdateReq {
  ConfigAction action = 1;
  Tax tax = 2;
}

message Tax {
  uint64 tax_config_id = 1;         // Tax config id (db.id)
  string start_datetime_utc = 2;    // Start datetime yyyy-MM-dd HH:mm:ss in UTC
  string end_datetime_utc = 3;      // End datetime yyyy-MM-dd HH:mm:ss in UTC
  UDec128 tax_percentage = 4;        // Tax percentage, int value in int, 10 == 10%
  string cache_key = 5;             // mkt|memberTypeCode
  string user_login = 6;            // base_user.login
}

// 4. Fee Assigment Config, Fee Assignment is configured from BE (there is 14 use case), value in config here is the priority from 14 use case
message FeeAssignmentUpdateReq {
  ConfigAction action = 1;
  FeeAssignment fee_assigment = 3;
}

message FeeAssignment {
  uint64 assigment_id = 1;
  string fee_activity = 2; // place_order|fill_fee
  string cache_key = 3;    // The 14 Priority cache key, refer to Query
  string fee_code_attr_pair = 4; // fee assignment code|attr
}

// [OV_LOCAL_FEE_AGENT] Response of Internal OV Fee Local Agent
message FeeResponse {
  UDec128 fees_amt = 1; // Current Order Fee
  map<string, UDec128> fees_details = 2; // example value {"BROKER FEE": 10,"PLATFORM FEE": 10,"ORDER FEE": 10, "Tax": 1}
  UDec128 tax_amt = 3;        // Current Order Tax
  uint64 tax_config_id = 4;         // Current Order Tax config id (db.id)
  UDec128 tax_percentage = 5; // Current Order Tax Percentage
  repeated FeeStructure trade_fee_rules_conf = 6; // because could have same code attribute and different fee type, example 1 code|attr // have broker fee using banded,  platform fee using tiered, order fee using ratio
  repeated Tax trade_tax_conf = 7;
  string fee_currency = 8;
}

service MatchService {
  rpc Test(TestReq) returns (TestResp);
  rpc NewSession(stream RequestList) returns (stream ResponseList);
  rpc Deposit(DepositReq) returns (DepositResp);
  rpc Withdraw(WithdrawReq) returns (WithdrawResp);
  rpc UpdateBalances(UpdateBalancesReq) returns (UpdateBalancesResp);
  rpc GetTickers(GetTickersReq) returns (GetTickersResp);
  rpc GetOrderbook(GetOrderbookReq) returns (GetOrderbookResp);
  rpc ClearOrderbook(ClearOrderbookReq) returns (ClearOrderbookResp);
  rpc ClearBalance(ClearBalanceReq) returns (ClearBalanceResp);
  rpc Reload(ReloadReq) returns (ReloadResp);
  rpc Exit(ExitReq) returns (ExitResp);
}

// Cache Snapshot
message MarketSnapshot {
  int64 id = 1;
  string name = 2;
  string code = 3;
  string market_id = 4;
  string timezone_name = 5;
  RoutineSnapshot active_routine = 6;
  bool allow_self_trade = 7;
  int64 operator_id = 8;
  string operator_code = 9;
  bool enable_fees = 10;
  bool enable_risk_control = 11;
  bool skip_sell_fees_earmark = 12;
  bool enable_custody = 13;
  bool position_keeping = 14;
  bool enable_market_surveillance = 15;
  string clob_order_types = 16;
  repeated string order_types = 17;
  repeated string market_tifs = 18;
  repeated string limit_tifs = 19;
  repeated string stop_tifs = 20;
  string order_expiry_time = 21; // HH:mm:dd in mktTimeZone Time
  map<string, AssetSnapshot> assets = 22;
  int64 day_order_utc_expiry_time = 23;
  bool enable_ext_settlement_confirm = 24;
  string market_model = 25;
}

message RoutineSnapshot {
  int64 routine_id = 1;
  string market_id = 2;
  string state_name = 3;
  bool allow_self_trade = 4;
  bool allow_time_in_force_gtc = 5;
  bool allow_time_in_force_gtd = 6;
  bool allow_time_in_force_ioc = 7;
  bool allow_time_in_force_day = 8;
  bool allow_ord_type_market = 9;
  bool allow_ord_type_limit = 10;
  bool allow_create = 11;
  bool allow_cancel = 12;
  bool allow_amend = 13;
  bool allow_match = 14;
  bool use_eq_price = 15;
}

message UserSnapshot {
  string user_id = 1;
  bool cod_enabled = 2;
  string sender_comp_id = 3;
}

message SelfTradeMapSnapshot {
  map<string, bool> self_trade_map = 1;
}

message AssetSnapshot {
  string asset_id = 1;
  string balance_account_code = 2;
  int64 asset_amount_decimals = 3;
  int64 Id = 4;
  string instrument_type = 5;
  string bond_daycount = 6;
  UDec128 coupon_rate = 7;
  string coupon_rate_type = 8;
  string dated_date = 9;
  UDec128 face_value = 10;
  string interest_calculation_time = 11;
  bool is_trade_on_clean_price = 12;
  string issue_date = 13;
  string maturity_date = 14;
  string timezone = 15;
  int64 denomination_currency_id = 16;
  string denomination_currency_code = 17;
  repeated CouponDate coupon_dates = 18;

}

message TickerSnapshot {
  string symbol = 1;
  string market_code = 2;
  UDec128 fee_rate = 3;
  UDec128 buffer_rate = 4;
  UDec128 qty_min = 5;
  UDec128 qty_max = 6;
  UDec128 qty_mult = 7;
  int32 qty_decimals = 8;
  int32 price_decimals = 9;
  UDec128 price_mult = 10;
  string instrument_type = 11;
  bool trade_on_clean_price = 12;
}

message HoldingAccountSnapshot {
  string  holding_account_id = 1;
  // no use
  // string service_account_id = 2;
  // No Use
  //map<string,BalanceAccountSnapshot> balance_accounts = 3;
  map<string, HoldingAccountMarketSnapshot> markets = 2;
  string account_type = 3;
  string account_type_code = 4;
  string state = 5;
  string ext_ov_name = 6;
  int64 id = 7;
  string number = 8;
  string uuid = 9;
  bool can_deposit = 10;
  bool can_transfer = 11;
  bool can_withdraw = 12;
  string status = 13;
  string saa_uuid = 14;
  string mkt_code = 15;
}

message HoldingAccountMarketSnapshot {
  string holding_account_id = 1;
  string market_code = 2;
  // cannot use this have circular
  // ServiceAccountSnapshot default_service_account = 3;
  UDec128 tax_rate = 3;
  string tax_code = 4;
  uint64 tax_db_id = 5;
  string default_saa_uuid = 6;
}

message ServiceAccountSnapshot {
  string  service_account_id = 1;
  string  api_key = 2;
  bool  allow_buy = 3;
  bool  allow_sell = 4;
  bool  allow_trade = 5;
  UDec128  maker_fee_rate = 6;
  UDec128  taker_fee_rate = 7;
  //cannot use this have circular
  //map<string, HoldingAccountSnapshot> holding_accounts = 8;
  string service_access_account_type_code = 8;
  string member_type_code = 9;
  int64 id = 10;
  string name = 11;
  string uuid = 12;
  string status = 13;
  bool can_buy = 14;
  bool can_sell = 15;
  int64 member_id = 16;
  string member_name = 17;
  string member_code = 18;
  int64 market_id = 19;
  string market_name = 20;
  string market_code = 21;
  repeated int64 aha_ids = 22;
  repeated string aha_uuids = 23;
}

message MemberTypeSnapshot {
  string MemberTypeCode = 1;
}

message FeeAssignmentSnapshot {
  uint64 assigment_id = 1;
  string code_attr_pair = 2;
  string map_cache_key = 3;
}

message ActivityFeeRuleContainerSnapshot {
  repeated ActivityFeeRuleSnapshot fee_rules = 1;
}

message ActivityFeeRuleSnapshot {
  uint64 rule_id = 1;
  FeeType fee_rule_type = 2;
  UDec128 min_fee = 3;
  UDec128 max_fee = 4;
  repeated FeeLevelsSnapshot fee_levels = 5;
  UDec128 ratio_x = 6;
  UDec128 ratio_y = 7;
  string  description = 8;
  string  currency = 9;
  bool    derive_on_amount = 10;

}

message FeeLevelsSnapshot {
  UDec128 min_amount = 1;
  UDec128 max_amount = 2;
  UDec128 fixed_fee = 3;
  UDec128 basis_point = 4;
  uint64  level_id = 5;
}

message MemberTypeTaxSnapshotList {
  repeated MemberTypeTaxSnapshot member_type_tax_snapshots = 1;
}

message MemberTypeTaxSnapshot {
  uint64 id = 1;
  string start_datetime = 2;
  string end_datetime = 3;
  UDec128 percentage = 4;
  string  mapCacheKey = 5;
  string  user_login = 6;
}

message OVOrderBookSnapshot {
  string symbol = 1;
  repeated PriceLevel bid_levels = 2;
  repeated PriceLevel ask_levels = 3;
  map<string, OVOpenOrderSnapshot> open_orders = 4;
}

message OVOpenOrderSnapshot {
  string symbol = 1;
  string cl_ord_id = 2;
  UDec128 open_qty = 3 ;
  UDec128 price = 4;
  string  order_id = 5;
  UDec128 interest_amt = 6;
}

message OREOrderBookSnapshot {
  string symbol = 1;
  uint64 last_trade_time = 2;
  repeated PriceLevelSnapShot bid_levels = 3;
  repeated PriceLevelSnapShot ask_levels = 4;
  UDec128 last_trade_price = 5;
  UDec128 last_trade_qty = 6;
}

message PriceLevelSnapShot {
  UDec128 price = 1;
  UDec128 qty = 2;
  string venue_market_code = 3;
}

message PositionKeepingInfo {
  bool pk_enabled = 1; // flag for PK to know whether required to calculate position or not
  string contract_ref = 2; // [Contract Id for deposit & withdraw] | [Ticker Code for Trade]
  Side side = 3; // required only for trade
  UDec128 qty = 4; // [amount of the deposit/withdraw] | [qty of the trade]
  UDec128 price = 5; // [cost of the deposit] | [price of the trade]
  string service_account_id = 6; // required for all saa.uuid
  bool wd_include_to_realize_pnl = 7; // only for withdrawal. flag for including the wd amount to realize PnL
  UDec128 fee = 8;
  UDec128 tax = 9;
}

message TradesConfirmReq {
  string  trade_id = 1;
  repeated MakerTakerTrade trades = 2;
}

enum TradeConfirmStatus {
  TRADE_CONFIRM_STATUS_DONE = 0;
  TRADE_CONFIRM_STATUS_ERROR = 1;
}

message TradeConfirmResp {
  string trade_id = 1;
  string order_id = 2;
  TradeConfirmStatus status = 3;
  string error_code = 4;
  string remark = 5;
}

message MakerTakerTrade {
  string  order_id = 1;
  string  market_code = 2;
  string  symbol = 3; // ticker code
  string  service_account_id = 4; // saa.uuid
  string  base_balance_account_id = 5;  // opr_code:aha_name:aml_code for base contract of the ticker
  string  quote_balance_account_id = 6; // opr_code:aha_name:aml_code for quote contract of the ticker
  UDec128 fill_qty = 7;
  UDec128 fill_price = 8;
  UDec128 fill_fee = 9;
  UDec128 fill_tax = 10;
  Side    order_side = 11;
  bool    is_taker = 12;
  string  account_id = 13; // aha.name
  TradeConfirmInfo trade_confirm_info = 14; // used for send notification to FE during trade settlement confirm
}


// AGX PROD BALANCE MIGRATION TO OV
// use for balance migration AGX PROD,
// initial is aim to set OV balance from db if OV in-memory balance is empty and db_wr shall by pass
// replace is aim to replace OV balance from db and db_wr shall by pass
enum MigrateType {
  INITIALIZE = 0;
  REPLACE = 1;
}

message AccountBalance {
  string      account_id = 1; // td.account.number
  string      bal_acct_id = 2; // opr_code:td.account.number:aml_code
  string      aml_code = 4; // db_balance type
  UDec128     current_bal = 5; // current_bal
  UDec128     earmark_bal = 6; // current_bal - earmark = available
  UDec128     pending_in = 7; // pending_in
  UDec128     pending_out = 8; // pending_out
}

message MigrateBalanceReq {
  ConfigAction action = 1;
  MigrateType  migrate_type = 2;
  repeated AccountBalance balances = 3;
  int64 ServiceRegistryID = 4;
}

message ClearPrice {
  string symbol = 1; // for during clear orderbook to tell OV to clear price by symbol
}

message OrderRoutingStrategyUpdateRequest {
  ConfigAction action = 1;
  int64  db_id = 2;
  string code = 3;
  string name = 4;
  string type = 5;
  OrdType order_type = 6;
  string time_in_force = 7;
  string ors_order_type = 8;
  repeated string venue_market_codes = 9;
  repeated string ticker_codes = 10;
  repeated SequenceVenueMarket sequence_venue_markets = 11;
}

message SequenceVenueMarket {
  int64 sequence = 1;
  string venue_market_code = 2;
}

message VenueMarketUpdateRequest {
  ConfigAction action = 1;
  int64  db_id = 2;
  bool balance_check = 3;
  string code = 4;
  string name = 5;
  string status = 6;
  int64 market_id = 7;
  string pricing_structure = 8;
  repeated TickerRoute tickers_route = 9;
  string price_streaming_order_types = 10;
  string market_code = 11;
}

message TickerRoute {
  int64 ticker_id = 1;
  string ticker_code = 2;
  string conversion_factor = 3;
  string lp_ticker_name = 4;
}

message VenueAccountUpdateRequest {
  ConfigAction action = 1;
  int64  db_id = 2;
  string aha_number = 3;
  string venue_market_code = 4;
  string code = 5;
  string name = 6;
  string member_code = 7;
  string market_code = 8;
  string entity_type = 9;
  string venue_account_name = 10;
}
// Earmark API. Use for Earmark or reverse Earmark from market-ops-v2
enum ApiEarmarkStatus {
  EARMARK_STATUS_DONE = 0;
  EARMARK_STATUS_ERROR = 1;
}
enum ApiEarmarkTxnType {
  EARMARK = 0;
  REVERSE = 1;
}

message ApiEarmark {
  uint64 db_id = 1;
  ApiEarmarkTxnType txn_type = 2;
  string client_ref = 3; // td.earmark.client_ref
  string hx_ref = 4; // td.earmark.hx_ref
  string service_account_id = 5; // saa uuid
  string account_id = 6; // aha uuid
  string balance_account_id = 7; // opr:aha_uuid:aml_code
  string contract_code = 8;
  string aml_code = 9;
  UDec128 amount = 10;
  UDec128 cost = 11;
  uint64  operator_db_id = 12;
  uint64  account_db_id = 13;
  uint64  service_account_db_id = 14;
  uint64  contract_db_id = 15;
  uint64  aml_db_id = 16;
  uint64  td_rest_req_db_id = 17;
  string  cl_req_id = 18;
  string  rest_txn_ref = 19;
}


message ApiEarmarkResp {
  ApiEarmark api_earmark = 1;
  ApiEarmarkStatus status = 2;
  string bal_update_id = 3;
  string error_code = 4;
  string remark = 5;
}


message AccruedInterestReq {
  string txn_ref = 1;
  string market_code = 2;
  string contract_code = 3;
}

message AccruedInterestResp {
  AccruedInterestReq request = 1;
  string accrued_interest = 2;
}


// USE FOR OV REJECT IN TERM OF CAN'T REACH TO MATCHING ENGINE!.
message OVLeaderReject {
  string text = 1;
  bool required_reverse_earmark = 2;
  Order order = 3;
}

// USE FOR OV REJECT IN TERM OF CAN'T REACH TO SOR!.
message OVLeaderRejectPS {
  bool required_reverse_earmark = 1;
  string error = 2;
  PsOrder ps_order = 3;
}

message SORReject {
  oneof reject {
    PsParentOrderExecReport parent_order_exec_report = 1;
    PsOrder ps_order = 2;
  }
}