package io.hydrax.pricestreaming.events;

import static org.mockito.Mockito.*;

import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.domain.MarketDTO;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.proto.metwo.match.*;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SnapshotEventTest {
  @Spy OrderBookCache orderBookCache;
  @Spy TradingVenueCache tradingVenueCache;
  @Spy TradingVenueAccountCache tradingVenueAccountCache;
  @Spy OrderRoutingStrategyCache orderRoutingStrategyCache;
  @Mock OrderCache orderCache;
  @Spy MarketCache marketCache;
  @Spy TickerCache tickerCache;
  @Spy SequenceCache sequenceCache;
  @InjectMocks SnapshotEvent snapshotEvent;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testOnTakeSnapshot() {
    when(tradingVenueCache.getAll())
        .thenReturn(
            List.of(
                TradingVenueDTO.builder()
                    .code("SFOX")
                    .id(1)
                    .balanceCheckEnabled(false)
                    .name("")
                    .status("")
                    .marketId(1)
                    .pricingStructure("")
                    .priceStreamingOrderTypes("")
                    .tickersRoute(List.of())
                    .build()));
    when(tradingVenueAccountCache.getAll())
        .thenReturn(List.of(TradingVenueAccountDTO.builder().venueAccount("123").id(1).build()));
    //    when(orderRoutingStrategyCache.getAll())
    //        .thenReturn(List.of(OrderRoutingStrategy.builder().code("Sequence").build()));
    when(marketCache.getAll())
        .thenReturn(
            List.of(
                MarketDTO.builder()
                    .code("Broker_Test")
                    .name("Broker")
                    .code("Broker_Test")
                    .timezone("Asia/Singapore")
                    .marketModel("broker")
                    .build()));
    List<ORESnapshot.Builder> result = snapshotEvent.onTakeSnapshot(new ArrayList<>());
    Assertions.assertEquals(
        ORESnapshot.newBuilder()
            .addAllVenueMarketUpdates(
                List.of(
                    VenueMarketUpdateRequest.newBuilder()
                        .setDbId(1)
                        .setCode("SFOX")
                        .setMarketId(1)
                        .build()))
            .build(),
        result.get(1).build());
    Assertions.assertEquals(
        ORESnapshot.newBuilder()
            .addAllVenueAccountUpdates(
                List.of(
                    VenueAccountUpdateRequest.newBuilder()
                        .setDbId(1)
                        .setVenueAccountName("123")
                        .build()))
            .build(),
        result.get(2).build());
  }
}
