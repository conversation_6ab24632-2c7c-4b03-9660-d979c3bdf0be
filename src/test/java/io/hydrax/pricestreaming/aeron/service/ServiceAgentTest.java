package io.hydrax.pricestreaming.aeron.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import io.aeron.cluster.service.ClusteredServiceContainer;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.aeron.setup.AeronConfig;
import io.hydrax.pricestreaming.cache.SequenceCache;
import io.vertx.core.eventbus.EventBus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ServiceAgentTest {
  @Mock ClusteredServiceContainer clusteredServiceContainer;
  @Mock EventBus eventBus;
  @Mock ClientManager clientManager;
  ServiceAgent erServiceAgent;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    erServiceAgent =
        new ServiceAgent(
            AeronConfig.getAeronClientProperty(), eventBus, clientManager, new SequenceCache());
  }

  @Test
  void clusteredServiceContainer() {
    try (MockedStatic<ClusteredServiceContainer> mocked =
        mockStatic(ClusteredServiceContainer.class)) {
      mocked
          .when(() -> ClusteredServiceContainer.launch(any()))
          .thenReturn(clusteredServiceContainer);
      ClusteredServiceContainer another = erServiceAgent.clusteredServiceContainer();
      assertEquals(clusteredServiceContainer, another);
    }
  }
}
