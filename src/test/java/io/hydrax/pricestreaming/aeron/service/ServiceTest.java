package io.hydrax.pricestreaming.aeron.service;

import static org.junit.jupiter.api.Assertions.*;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.cache.SequenceCache;
import io.vertx.core.eventbus.EventBus;
import org.agrona.concurrent.AtomicBuffer;
import org.agrona.concurrent.UnsafeBuffer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ServiceTest {
  @Mock EventBus eventBus;
  @Mock ClientManager clientManager;

  SORService sorService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    sorService = new SORService(eventBus, clientManager, new SequenceCache());
  }

  @Test
  void onStart() {
    assertDoesNotThrow(() -> sorService.onStart(null, null));
  }

  @Test
  void onSessionOpen() {
    assertDoesNotThrow(() -> sorService.onSessionOpen(null, 0));
  }

  @Test
  void onSessionClose() {
    assertDoesNotThrow(() -> sorService.onSessionClose(null, 0, null));
  }

  @Test
  void onSessionMessage() {
    AtomicBuffer buffer = new UnsafeBuffer(new byte[10]);
    assertDoesNotThrow(() -> sorService.onSessionMessage(null, 0, buffer, 0, 0, null));
  }

  @Test
  void onTimerEvent() {}

  @Test
  void onTakeSnapshot() {}

  @Test
  void onRoleChange() {}

  @Test
  void onTerminate() {}
}
