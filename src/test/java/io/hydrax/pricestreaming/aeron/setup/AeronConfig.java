package io.hydrax.pricestreaming.aeron.setup;

import io.hydrax.aeron.common.IdelStrategyEnum;
import io.hydrax.aeron.config.AeronClientProperty;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class AeronConfig {
  public static AeronClientProperty getAeronClientProperty() {
    return new AeronClientProperty() {
      @Override
      public String directory() {
        return "/dev/shm/aeron";
      }

      @Override
      public Map<String, Client> clients() {
        Map<String, Client> clients = new HashMap<>();
        clients.put(
            "OM",
            new AeronClientProperty.Client() {
              @Override
              public Optional<String> egressChannel() {
                return Optional.of("aeron:udp?endpoint=localhost:0");
              }

              @Override
              public String ingressChannel() {
                return "aeron:udp";
              }

              @Override
              public String ingressEndpoints() {
                return "0=localhost:20121";
              }

              @Override
              public Integer ingressStreamId() {
                return 101;
              }

              @Override
              public Integer poolSize() {
                return 1;
              }

              @Override
              public String mode() {
                return "cluster";
              }

              @Override
              public String idleStrategy() {
                return IdelStrategyEnum.YIELD.getName();
              }

              @Override
              public String channel() {
                return "aeron:udp?endpoint=localhost:0";
              }

              @Override
              public Integer streamId() {
                return 40;
              }

              @Override
              public String requestChannel() {
                return "";
              }

              @Override
              public Integer requestStreamId() {
                return null;
              }

              @Override
              public Integer responseStreamId() {
                return null;
              }

              @Override
              public String responseChannel() {
                return null;
              }

              @Override
              public Integer bufferSize() {
                return 262144;
              }

              @Override
              public String targetName() {
                return "OM";
              }
            });
        return clients;
      }

      @Override
      public Cluster cluster() {
        return new AeronClientProperty.Cluster() {
          @Override
          public String directory() {
            return "cluster";
          }

          @Override
          public Integer id() {
            return 0;
          }

          @Override
          public String members() {
            return "localhost";
          }
        };
      }

      @Override
      public Archive archive() {
        return new AeronClientProperty.Archive() {
          @Override
          public String controlRequestChannel() {
            return "aeron:ipc?alias=cluster-service-archive-ctrl-req|term-length=128k";
          }

          @Override
          public String controlResponseChannel() {
            return "aeron:ipc?alias=cluster-service-archive-ctrl-resp|term-length=128k";
          }

          @Override
          public String archiveDirectory() {
            return "/data/archive";
          }
        };
      }

      @Override
      public Service service() {
        return () -> "yield";
      }
    };
  }
}
