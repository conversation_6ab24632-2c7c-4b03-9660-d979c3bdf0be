package io.hydrax.pricestreaming.router;

import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.cache.*;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.domain.OrderRoutingStrategyDTO;
import io.hydrax.pricestreaming.domain.TradingVenueAccountDTO;
import io.hydrax.pricestreaming.service.OrderService;
import io.hydrax.pricestreaming.service.PostRoutingService;
import io.hydrax.pricestreaming.utils.BeanUtil;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.MultiMap;
import java.util.List;
import java.util.function.Supplier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RoutingEngineTest {
  @Mock ClientManager clientManager;
  @Mock OrderService orderService;
  @Mock MarketCache marketCache;
  @Spy OrderRoutingStrategyCache orderRoutingStrategyCache;
  @Mock Supplier<List<Rule>> rules = () -> orderRoutingStrategyCache.getAll();
  @Mock PostRoutingService postRoutingService;
  RoutingEngine routingEngine;
  SequenceStrategy sequenceStrategy;
  OrderRoutingStrategyDTO strategy;

  @BeforeEach
  void setUp() {
    routingEngine = new RoutingEngine(() -> orderRoutingStrategyCache.getAll());
    MockitoAnnotations.openMocks(this);
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(ObjectMapper.class)).thenReturn(new ObjectMapper());
      strategy = OrderRoutingStrategyDTO.from(buildStrategy());
      SequenceVenueCache sequenceVenueCache = new SequenceVenueCache();
      sequenceVenueCache.put("sequence", List.of("SFOX"));
      TradingVenueCache tradingVenueCache = new TradingVenueCache();
      tradingVenueCache.put(
          VenueMarketUpdateRequest.newBuilder()
              .setAction(ConfigAction.UPDATE)
              .setCode("SFOX")
              .setDbId(1)
              .setName("SFOX")
              .setPriceStreamingOrderTypes(
                  """
{"market":{"enabled":true,"tifs":["gtc"]},"limit":{"enabled":true,"tifs":["fok","gtc","ioc"]},"stop":{"enabled":false,"tifs":[]}}
""")
              .addTickersRoute(
                  TickerRoute.newBuilder().setLpTickerName("11111").setTickerCode("ticker").build())
              .build());

      TradingVenueAccountCache tradingVenueAccountCache = new TradingVenueAccountCache();
      tradingVenueAccountCache.put(
          TradingVenueAccountDTO.from(
              VenueAccountUpdateRequest.newBuilder()
                  .setAction(ConfigAction.UPDATE)
                  .setCode("SFOX")
                  .setDbId(1)
                  .setName("SFOX")
                  .setVenueAccountName("SFOX")
                  .setVenueMarketCode("SFOX")
                  .build()));

      sequenceStrategy =
          new SequenceStrategy(sequenceVenueCache, tradingVenueCache, postRoutingService);
    }
  }

  @Test
  void testRoute() {
    when(orderRoutingStrategyCache.getAll()).thenReturn(List.of(strategy));
    try (MockedStatic<BeanUtil> mocked = mockStatic(BeanUtil.class)) {
      mocked.when(() -> BeanUtil.getBean(SequenceStrategy.class)).thenReturn(sequenceStrategy);
      routingEngine.route(
          Order.builder()
              .psOrder(buildPsOrder())
              .headers(
                  MultiMap.caseInsensitiveMultiMap()
                      .add("orderId", IdUtil.formatId(Constant.ID_SEQUENCE.getAndIncrement())))
              .build(),
          List.of("SFOX"));
      verify(postRoutingService, times(1)).postProcess(any(), any(), any());
    }
  }

  private PsOrder buildPsOrder() {
    return PsOrder.newBuilder()
        .setOrderId("*********")
        .setSide(Side.SIDE_BUY)
        .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
        .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
        .setPrice(UDec128Util.from("123.45"))
        .setQty(UDec128Util.from("100.00"))
        .setSymbol("PS04_BROKER:BTC_PS04/USD_PS04")
        .build();
  }

  private static OrderRoutingStrategyUpdateRequest buildStrategy() {
    return OrderRoutingStrategyUpdateRequest.newBuilder()
        .setCode("sequence")
        .setName("sequence")
        .setType("sequence")
        .addAllTickerCodes(List.of("PS04_BROKER:BTC_PS04/USD_PS04"))
        .addAllVenueMarketCodes(List.of("SFOX"))
        .setDbId(1)
        .setOrsOrderType(
            """
{"market": {"tifs": ["day", "gtc", "fok", "ioc"], "enabled": true}, "limit": {"tifs": ["day", "gtc", "fok", "ioc"], "enabled": true}, "stop": {"enabled": false}}
""")
        .addAllTickerCodes(List.of("PS04_BROKER:BTC_PS04/USD_PS04"))
        .addSequenceVenueMarkets(
            SequenceVenueMarket.newBuilder().setSequence(1).setVenueMarketCode("SFOX").build())
        .build();
  }
}
